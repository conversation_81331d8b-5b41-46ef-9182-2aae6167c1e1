import { Maybe, Undef } from 'types/UtilityTypes';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';

export interface FormStorageServiceInterface {
  storageKey: string;
  saveFormState<T, U>(key: string, formState: SavedFormState<T, U>): void;
  getFormState<T, U>(key: string): Undef<SavedFormState<T, U>>;
  clearFormState(key: string): void;
}

export type SavedFormState<T, U> = {
  threadID: string;
  clickedSuggestion: Maybe<GenericSuggestion>;
  values: T;
  dirtyFields: U;
};
