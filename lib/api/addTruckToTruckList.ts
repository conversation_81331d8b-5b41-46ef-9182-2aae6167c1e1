import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { Truck } from 'types/Truck';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export type AddTruckToTruckListResponse = {
  createdTruck: Truck;
};

export default async function addTruckToTruckList(
  emailID: number,
  threadID: string,
  pickupDate: string
): Promise<Result<AddTruckToTruckListResponse, ApiError>> {
  const params = [
    `threadID=${encodeURIComponent(threadID)}`,
    `emailID=${emailID}`,
  ];

  try {
    const response = await axios.post(
      `/trucklist/truck/create?${params.join('&')}`,
      { pickupDate }
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'addTruckToTruckList' });

    return err({
      message: 'Oops, something went wrong!',
    });
  }
}
