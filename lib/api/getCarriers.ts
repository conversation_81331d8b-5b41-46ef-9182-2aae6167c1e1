import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { TMSCarrier } from 'types/Load';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

type GetCarriersResponse = {
  carrierList: TMSCarrier[];
  tmsTenant: string;
};

export async function getCarriers(
  tmsID: number,
  forceRefresh?: boolean
): Promise<Result<GetCarriersResponse, ApiError>> {
  try {
    const response = await axios.get<GetCarriersResponse>(
      `/carriers?tmsID=${tmsID}${forceRefresh ? '&forceRefresh=true' : ''}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getCarriers' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get list of carriers from TMS' });
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to get list of carriers from TMS',
    });
  }
}
