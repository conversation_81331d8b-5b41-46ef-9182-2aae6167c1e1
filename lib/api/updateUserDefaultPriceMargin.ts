import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ProfitType } from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export async function updateUserDefaultPriceMargin(
  defaultPriceMargin: number,
  defaultPriceMarginType: ProfitType
): Promise<Result<void, ApiError>> {
  const requestBody = { defaultPriceMargin, defaultPriceMarginType };

  try {
    const resp = await axios.patch('user/config/price-margin', requestBody);

    return ok(resp.data);
  } catch (error) {
    captureException(error, { functionName: 'updateUserDefaultPriceMargin' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to update user default price margin' });
    }

    if (error && isAxiosError(error) && error.response?.status === 401) {
      throw error;
    }

    if (error && error.message === 'Extension context invalidated.') {
      throw error;
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to update user default price margin',
    });
  }
}
