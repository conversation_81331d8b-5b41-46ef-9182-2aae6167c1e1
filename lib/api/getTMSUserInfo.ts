// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { ApiError } from 'types/api/ApiError';

type GetRevenueCodeResponse = {
  revenueCode: string;
  externalTMSId: string;
};

export const getTMSUserInfo = async (): Promise<
  Result<GetRevenueCodeResponse, ApiError>
> => {
  try {
    const response = await axios.get(`/user/tms-info`);
    return ok(response.data);
  } catch (error: unknown) {
    if (error && isAxiosError(error)) {
      return err({ message: error.response?.data.message });
    }
    return err({ message: 'Failed to get revenue code' });
  }
};
