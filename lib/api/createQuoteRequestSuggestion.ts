import { isAxiosError, isCancel } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { Maybe } from 'types/UtilityTypes';
import { ApiError } from 'types/api/ApiError';
import { QuotingPortals } from 'types/enums/Integrations';
import {
  QuoteChanges,
  SuggestedQuoteChange,
  SuggestionSourceCategories,
} from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';

export type CreateQuoteRequestSuggestionRequest = Omit<
  QuoteChanges,
  'pickupDate' | 'deliveryDate'
> & {
  customer: {
    name: string;
  };
  // When passing date objects across Chrome contexts (e.g. from background script to SidePanel which
  // technically runs as a content script, Dates must be properly serialized otherwise they show up as undefined)
  pickupDate: Maybe<string>; // ISO string
  deliveryDate: Maybe<string>; // ISO string
  sourceCategory: SuggestionSourceCategories;
  source: QuotingPortals;
  sourceExternalID: string;
  sourceURL: string;
  htmlSnippet: string; // Do NOT include the entire page, just the relevant snippet for capturing quote request data
  selectedQuickQuoteId?: number;
};

export async function createQuoteRequestSuggestion(
  data: CreateQuoteRequestSuggestionRequest
): Promise<Result<SuggestedQuoteChange, ApiError>> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 500); // 0.5 second timeout

  try {
    const response = await axios.post<SuggestedQuoteChange>(
      '/suggestions/quoting-portal/quote-request',
      data,
      {
        signal: controller.signal,
      }
    );

    clearTimeout(timeoutId);
    return ok(response.data);
  } catch (error) {
    clearTimeout(timeoutId);
    captureException(error, { functionName: 'createQuoteRequestSuggestion' });

    if (isCancel(error)) {
      return err({ message: 'Request timed out after 0.5 seconds' });
    }

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to create quote request suggestion' });
    }

    return err({
      message:
        error.response?.data.message ||
        'Failed to create quote request suggestion',
    });
  }
}
