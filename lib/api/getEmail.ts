import { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import { Email } from 'types/Email';
import { Maybe } from 'types/UtilityTypes';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export default async function getEmail(
  threadId: string
): Promise<Result<Maybe<Email>, ApiError>> {
  try {
    const response = await axios.get(
      `email/thread/${encodeURIComponent(threadId!)}`
    );

    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getEmail' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get email' });
    }

    return err({
      message: error.response?.data.message || 'Failed to get email',
    });
  }
}
