import axios, { isAxiosError } from 'axios';
import { Result, err, ok } from 'neverthrow';

import { QuoteRequest } from 'types/QuoteRequest';
import { ApiError } from 'types/api/ApiError';
import captureException from 'utils/captureException';

export async function getQuoteRequest(
  threadID: string
): Promise<Result<QuoteRequest, ApiError>> {
  try {
    const response = await axios.get(
      `/quote/request/thread/${encodeURIComponent(threadID)}`
    );
    return ok(response.data);
  } catch (error) {
    captureException(error, { functionName: 'getQuoteRequest' });

    if (!isAxiosError(error)) {
      return err({ message: 'Failed to get quote request' });
    }

    return err({
      message: error.response?.data.message || 'Failed to get quote request',
    });
  }
}
