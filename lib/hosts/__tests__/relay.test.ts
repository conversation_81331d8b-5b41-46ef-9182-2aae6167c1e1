import { Pickup } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';

import { HostInterface } from '../interface';
import { Relay } from '../relay';

describe('Relay TMS', () => {
  let relay: HostInterface;

  beforeEach(() => {
    relay = new Relay();
    location.href = '';
    document.body.innerHTML = '';
  });

  describe('parseExternalTMSID', () => {
    beforeEach(() => {
      // Reset document and location before each test
      relay = new Relay();
      location.href = '';
      document.body.innerHTML = '';
      delete (global as any).location;
      (global as any).location = { href: '' };
    });

    test('should extract TMS ID from load_board_load_detail URL', () => {
      location.href =
        'https://training.relaytms.com/sourcing/load_board_load_detail/2301234';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('2301234');
    });

    test('should extract TMS ID from planning_board/stop_management URL', () => {
      location.href =
        'https://training.relaytms.com/planning_board/stop_management/2205432';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('2205432');
    });

    test('should return empty string if TMS ID is not found in URL', () => {
      location.href = 'https://training.relaytms.com/some_other_page';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('');
    });

    test('should return content from col load-number class when URL contains planning_board', () => {
      location.href =
        'https://training.relaytms.com/planning_board/filteredCustomerName';
      document.body.innerHTML = '<div class="col load-number"> 123456 </div>';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('123456');
    });

    test('should return empty string if col load-number class is not found', () => {
      location.href = 'https://training.relaytms.com/planning_board';
      document.body.innerHTML = '';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('');
    });

    test('should return content from relay-reference-number.d-inline class when URL contains tracking_load_detail', () => {
      location.href = 'https://training.relaytms.com/tracking_load_detail';
      document.body.innerHTML =
        '<span class="relay-reference-number d-inline"> 654321 </span>';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('654321');
    });

    test('should return empty string if relay-reference-number.d-inline class is not found', () => {
      location.href = 'https://training.relaytms.com/tracking_load_detail';
      document.body.innerHTML = '';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('');
    });

    test('should extract TMS ID from hub URL', () => {
      location.href = 'https://training.relaytms.com/hub/33098765';
      const result = relay.parseExternalTMSID();
      expect(result).toBe('3309876');
    });
  });

  test('Parse pickup details', () => {
    document.body.innerHTML = `
    <div><span/></div>
    <div>
        <main>
            <div>
                <main>
                    <div>
                        <div><span/></div>
                        <div><span/></div>
                        <div>
                            <div>
                                <div>
                                    <form>
                                        <div><span/></div>
                                        <div>
                                            <div>
                                                <div><span/></div>
                                                <div><span/></div>
                                                <div>
                                                    <div>
                                                        <div><span/></div>
                                                        <div><span/></div>
                                                        <div>
                                                          <div id="some-container">
                                                            <div class="pickup-name">Pickup Name</div>
                                                            <div class="pickup-address">123 Main St</div>
                                                            <div class="pickup-city-state">City, State</div>
                                                            <div class="pickup-phone">************</div>
                                                            <input id="schedule-plan-form_stops_0_reference" value="REF123" />
                                                          </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </main>
    </div>
  `;

    const pickup: Maybe<Pickup> = relay.parsePickup();
    expect(pickup).toEqual({
      name: 'Pickup Name',
      addressLine1: '123 Main St',
      city: 'City',
      state: 'State',
      phone: '************',
      refNumber: 'REF123',
    });
  });
});
