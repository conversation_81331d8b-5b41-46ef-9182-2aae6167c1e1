import { QuotingPortal } from 'lib/hosts/quoting/interface';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export const FreightViewSubmitAction = 'freightview-submit-quote';

export class FreightView implements QuotingPortal {
  submitAction = FreightViewSubmitAction;
  /**
   * Determines if a quote can be submitted to FreightView based on the current tab and document state.
   * @param tab The current Chrome tab
   * @param document The current document
   * @returns boolean indicating if a quote can be submitted
   */
  canSubmit(tab: Undef<chrome.tabs.Tab>, html: string): boolean {
    if (!tab?.url) return false;
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    try {
      const url = new URL(tab.url);
      const isFreightView = url.origin?.includes('carrier.freightview');

      if (!isFreightView) return false;

      // Check if we're on a quote page with the necessary elements
      const hasSelectedShipment =
        doc.querySelector('.shipment-item--selected') !== null;

      return hasSelectedShipment;
    } catch (error: any) {
      captureException('Error checking FreightView quote page:', error);
      return false;
    }
  }
}
