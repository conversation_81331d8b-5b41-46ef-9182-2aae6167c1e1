interface CanadaProvinceTimezones {
  [key: string]: string;
  AB: string;
  BC: string;
  MB: string;
  NB: string;
  NL: string;
  NS: string;
  NT: string;
  NU: string;
  ON: string;
  PE: string;
  QC: string;
  SK: string;
  YT: string;
}

export const CANADA_PROVINCE_ABBREVIATIONS: string[] = [
  'AB',
  'BC',
  'MB',
  'NB',
  'NL',
  'NS',
  'NT',
  'NU',
  'ON',
  'PE',
  'QC',
  'SK',
  'YT',
];

export const CANADA_PROVINCE_TIMEZONES: CanadaProvinceTimezones = {
  AB: 'America/Edmonton',
  BC: 'America/Vancouver',
  MB: 'America/Winnipeg',
  NB: 'America/Moncton',
  NL: 'America/St_Johns',
  NS: 'America/Halifax',
  NT: 'America/Yellowknife',
  NU: 'America/Iqaluit',
  ON: 'America/Toronto', // Note: NW Ontario uses Central Time
  PE: 'America/Halifax',
  QC: 'America/Toronto', // Northern QC may vary
  SK: 'America/Regina', // SK does not observe DST
  YT: 'America/Whitehorse',
};
