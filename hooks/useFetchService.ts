// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import axios from '@utils/axios';

import {
  CarrierQuoteConfig,
  IntegrationCore,
  QuickQuoteConfig,
  Service,
  ServiceFeatureConfigurations,
  ServiceFeaturesListType,
} from 'contexts/serviceContext';
import { Maybe } from 'types/UtilityTypes';

import { useServiceFeatures } from './useServiceContext';

type FetchServiceFeaturesResponse = {
  id: number;
  tmsIntegrations: IntegrationCore[];
  quotingIntegrations: IntegrationCore[];
  schedulerIntegrations: IntegrationCore[];
  configurations: ServiceFeatureConfigurations;
  quickQuoteConfig: QuickQuoteConfig;
  carrierQuoteConfig?: Maybe<CarrierQuoteConfig>;
} & ServiceFeaturesListType;

export default async function useFetchService(
  serviceID: number
): Promise<void> {
  const { setService } = useServiceFeatures();

  const axiosRes = await axios.get<FetchServiceFeaturesResponse>(
    `service/${encodeURIComponent(serviceID!)}/features`
  );

  const {
    data: {
      id,
      tmsIntegrations,
      quotingIntegrations,
      schedulerIntegrations,
      configurations,
      quickQuoteConfig,
      carrierQuoteConfig,
      ...features
    },
  } = axiosRes;

  setService(
    (prev) =>
      ({
        ...prev,
        serviceID: id,
        tmsIntegrations,
        quotingIntegrations,
        schedulerIntegrations,
        configurations,
        quickQuoteConfig,
        carrierQuoteConfig: carrierQuoteConfig,
        serviceFeaturesEnabled: features,
      }) as Service
  );
}
