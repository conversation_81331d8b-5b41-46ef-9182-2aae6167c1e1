import { useThrowableSWR } from 'utils/fetcher';

export function useFetchOrdersForLoad(loadID: number) {
  const { data, isLoading, error } = useThrowableSWR<any>(
    loadID ? `/load/${loadID}/orders` : null,
    false // Don't retry on 404s
  );

  // Support both array and { orders: array } response shapes
  const orders = Array.isArray(data) ? data : data?.orders || [];
  return { orders, isLoading, error };
}
