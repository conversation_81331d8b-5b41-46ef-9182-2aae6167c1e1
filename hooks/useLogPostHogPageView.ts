import { useEffect } from 'react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore posthog is in the parent dir
import { usePostHog } from 'posthog-js/react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import captureException from 'utils/captureException';

type PageViewProps = {
  [key: string]: any;
};

export default function useLogPostHogPageView(
  pageName: string,
  additionalProps?: Omit<PageViewProps, 'page_name'>,
  requiredKeys: (keyof PageViewProps)[] = []
): void {
  const posthog = usePostHog();

  useEffect(() => {
    if (!isProd()) {
      return;
    }

    // If there are required keys, check if they all exist in additionalProps
    if (requiredKeys.length > 0 && additionalProps) {
      const hasAllRequired = requiredKeys.every(
        (key) => !!additionalProps[key]
      );
      if (!hasAllRequired) return;
    }

    try {
      posthog.capture('$pageview', {
        $current_url: pageName,
        ...additionalProps,
      });
    } catch (e) {
      captureException(e, {
        functionName: 'useLogPostHogPageView',
        pageName,
        additionalProps,
      });
    }
  }, [posthog, pageName, additionalProps, requiredKeys]);
}
