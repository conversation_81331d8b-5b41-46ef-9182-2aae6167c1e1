export interface CompanyCoreInfo {
  externalTMSID: string;
  name: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  contact?: string;
  phone?: string;
  email?: string;
}

export interface ValueUnit {
  val: number;
  unit: string;
}

export interface Pickup extends CompanyCoreInfo {
  RefNumber?: string;
  ApptRequired: boolean;
  ApptType?: string;
  ApptNote?: string;
  Timezone?: string;
}

export interface Consignee extends CompanyCoreInfo {
  RefNumber?: string;
  ApptRequired: boolean;
  ApptType?: string;
  ApptNote?: string;
  Timezone?: string;
}

export interface Specifications {
  orderType: string;
  totalInPalletCount: number;
  totalOutPalletCount: number;
  totalPieces: ValueUnit;
  commodities: string;
  totalWeight: ValueUnit;
  totalDistance: ValueUnit;
  transportType?: string;
  transportSize?: string;
  isRefrigerated: boolean;
  minTempFahrenheit?: number;
  maxTempFahrenheit?: number;
  hazmat?: boolean;
}

export interface RateData {
  collectionMethod: string;
  revenueCode: string;
  customerRateType: string;
  customerLineHaulCharge: ValueUnit;
  customerRateNumUnits: number;
  customerLineHaulRate: number;
  customerLineHaulUnit: string;
  customerTotalCharge: ValueUnit;
}

export interface ExternalReference {
  ID: number;
  system: string;
  value: string;
  description?: string;
  isPrimary: boolean;
}

export interface Order {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;

  // Core Info
  status: string;
  mode: string;
  type: string;
  priority: string;
  reference: string;
  externalOrderId: string;
  orderTrackingId: string;

  // Company Info
  customer: CompanyCoreInfo;
  billTo: CompanyCoreInfo;
  pickup: CompanyCoreInfo;
  consignee: CompanyCoreInfo;

  // Dates
  pickupDate: string;
  dropoffDate: string;
  requestedPickupDate: string;
  requestedDeliveryDate: string;

  // Specifications
  specifications: Specifications;
  rateData: RateData;

  // Counts
  pieceCount: number;
  totalPackages: number;
  totalPallets: number;
  totalTotes: number;
  totalWeight: number;
  totalWeightLbs: number;
  totalVolume: number;
  handlingUnitCount: number;

  // Received Counts
  receivedTotalPackages: number;
  receivedTotalPallets: number;
  receivedTotalTotes: number;
  receivedTotalWeight: number;

  // Flags
  isBooked: boolean;
  isFulfilled: boolean;
  isHazmat: boolean;
  isHot: boolean;
  isInPlanning: boolean;
  isPrePayment: boolean;
  isVoided: boolean;
  moreThanTwoStops: boolean;
  needsReview: boolean;
  doNotOverwrite: boolean;

  // Additional Info
  poNums: string;
  notes: string | null;
  salesTeam: string;
  organizationName: string;
  freightTerms: string;
  currency: string;
  crossdockType: string;
  integrationStatus: string;
  orderTMSStatus: string;
  billingStatus: string;

  // IDs
  loadId: number;
  serviceID: number;
  carrierId: string;
  creatorId: string;
  customerId: string;
  truckBrokerId: string;
  externalLoadId: string;

  // Arrays
  pickups: any[] | null;
  deliveries: any[] | null;
  orderLines: any[] | null;
  billingItems: any[] | null;
  costItems: any[] | null;
  branches: any[] | null;
}
