export interface PendingEmail {
  id: number;
  recipient: string;
  subject: string;
  body: string;
  timestamp: string;
  scheduleSend: Date;
}

export interface PendingCarrierEmails {
  pickup: PendingEmail[];
  loaded: PendingEmail[];
  inTransit: PendingEmail[];
  dropoff: PendingEmail[];
  unloaded: PendingEmail[];
}

export type PendingOutboxEmails = {
  carrierEmails: PendingCarrierEmails;
};
