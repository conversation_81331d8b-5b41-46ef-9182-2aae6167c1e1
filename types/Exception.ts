import { Maybe } from './UtilityTypes';

export type Exception = {
  loadId: number;
  freightTrackingId: string;
  eventCode: string;
  source: string;
  isOnTime: Maybe<boolean>;
  whoEntered: string;
  carrier: string;
  driver: string;
  fault: string; // "Y or empty string"
  trailer: string;
  note: string;
  status: string; // consistent with events in aljex, but there's no way to edit this property
  dateTime: string; // RFC339 string representation
  dateTimeWithoutTimezone: string; // RFC3339, defaulted to UTC to be TZ-agnostic
};
