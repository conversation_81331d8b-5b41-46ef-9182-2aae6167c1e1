import { Maybe } from 'types/UtilityTypes';
import { SidepanelMessage } from 'types/chromescript/util';

export type SubmitQuoteToPortalData = {
  flatRate: number; // Line haul or all-in rate, depending on button user selected
  distance: Maybe<number>;
  isSubmitOnPortalEnabled: boolean;
};

export type SubmitQuoteToPortalMessage = SidepanelMessage & {
  data: SubmitQuoteToPortalData;
};

export type PortalActionResult = {
  success: boolean;
  partialSuccess?: boolean;
  error?: any;
};
