import { Truck, TruckListErrors } from './Truck';
import { MaybeUndef } from './UtilityTypes';

export type TruckListChanges = {
  'carrier.name': string;
  'carrier.contactEmail': string;
  'carrier.contactName': string;
  'carrier.mc': string;
  'carrier.dot': string;
  serviceName: string;
  trucks: string;
};

export type TruckList = {
  serviceName: string;
  carrier: TruckListCarrier;
  errors: MaybeUndef<TruckListErrors>;
  trucks: Truck[];
};

export type TruckListCarrier = {
  name: string;
  mc: string;
  dot: string;
  contactEmail: string;
  contactName: string;
};
