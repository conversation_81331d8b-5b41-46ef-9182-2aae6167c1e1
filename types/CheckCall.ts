import { Maybe } from './UtilityTypes';

export type CheckCall = {
  ID: number;
  loadID: number;
  freightTrackingID: string;
  city: string;
  state: string;
  status: string;
  author: string;
  reason: string; // i.e. EDI Reason for Aljex, Late Reason for Relay
  isOnTime: Maybe<boolean>;
  isException: Maybe<boolean>;
  dateTime: string; // RFC339 string representation of when the check call occurred
  // RFC3339, defaulted to UTC to be TZ-agnostic. Required
  dateTimeWithoutTimezone: string;
  // RFC3339, defaulted to UTC to be TZ-agnostic. Optional
  endDateTimeWithoutTimezone: string;

  // The ETA for the next stop, timezone-agnostic and normalized to UTC. Optional
  nextStopETAWithoutTimezone: string;
  nextStopID: 'pickup' | 'dropoff' | ''; // TODO support multiple stops ENGB-2361

  // When the check call was captured/inputted into TMS, parsed in the right timezone. For example, the check call
  // happened at 9 am but the user inputted it at 10:30 am.
  capturedDateTime: string; // RFC3339, with correct TZ

  // IANA timezone
  timezone: string;

  // Source of the update e.g. <PERSON><PERSON><PERSON>, dispatcher, driver, etc. Different from Author as <PERSON> may
  // input the check call into TMS, but he received the information from the carrier's dispatcher.
  source: string;
  notes: string;
};
