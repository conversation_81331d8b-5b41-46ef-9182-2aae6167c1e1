import { Attachment } from 'types/EmailAttachment';
import { Maybe } from 'types/UtilityTypes';

import { TruckListCarrier } from '../TruckList';
import { LoadBuildingSuggestions } from './LoadBuildingSuggestions';
import {
  CarrierChanges,
  CheckCallChanges,
  SuggestedLoadChange,
  SuggestionStatus,
} from './LoadSuggestions';
import { SuggestedQuoteChange } from './QuoteSuggestions';

export enum SuggestionPipelines {
  CarrierInfo = 'carrier_info_pipeline',
  ApptConfirmation = 'appointment_confirmation',
  CheckCall = 'check_call_pipeline',
  QuickQuote = 'quick_quote_pipeline',
  LoadBuilding = 'load_building_pipeline',
}

export enum SuggestionCategories {
  CarrierInfo = 'carrier_info',
  PickupInfo = 'pickup_info',
  CheckCall = 'check_call',
  QuickQuote = 'quick_quote',
  LoadBuilding = 'load_building',
}
export type GenericSuggestionCore = {
  id: number;
  createdAt: string;
  account: string;
  status: SuggestionStatus;
  emailID: number;
  latestEmailIDFromThread: number;
  pipeline: SuggestionPipelines;
  category: SuggestionCategories;
  attachment: Maybe<Attachment>;
};
export type GenericSuggestion =
  | SuggestedLoadChange
  | SuggestedQuoteChange
  | LoadBuildingSuggestions;

export type SuggestionChangeRecord = Record<
  string,
  | CarrierChanges[keyof CarrierChanges]
  | CheckCallChanges[keyof CheckCallChanges]
  | TruckListCarrier[keyof TruckListCarrier]
>;

export type SuggestionAppliedPair<T> = {
  suggestion: T;
  applied: T | string;
};
