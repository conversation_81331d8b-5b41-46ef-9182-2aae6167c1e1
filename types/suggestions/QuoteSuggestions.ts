import { TransportType } from 'types/QuoteRequest';
import { Maybe } from 'types/UtilityTypes';
import { QuotingPortals } from 'types/enums/Integrations';
import {
  GenericSuggestionCore,
  SuggestionCategories,
  SuggestionPipelines,
} from 'types/suggestions/CoreSuggestions';

export type SuggestedQuoteChange = GenericSuggestionCore & {
  pipeline: SuggestionPipelines.QuickQuote;
  category: SuggestionCategories.QuickQuote;
  suggested: QuoteChanges;
  applied?: QuoteChanges;
  latestEmailIDFromThread: number;
  source: QuotingPortals;
  sourceExternalID?: string;
  sourceURL?: string; // Only if portal has unique URL for each quote request
  sourceCategory: SuggestionSourceCategories;
};

export enum SuggestionSourceCategories {
  QuotingPortal = 'quoting-portal',
  Email = 'email',
}

export type QuoteChanges = {
  customerExternalTMSID: string;
  transportType: TransportType;
  pickupCity: string;
  pickupState: string;
  pickupZip: string;
  pickupDate: Maybe<Date>;
  deliveryCity: string;
  deliveryState: string;
  deliveryZip: string;
  deliveryDate: Maybe<Date>;
  fuelSurchargePerMile: Maybe<number>;
  fuelSurchargeTotal: Maybe<number>;
  distanceMiles: Maybe<number>;
};
