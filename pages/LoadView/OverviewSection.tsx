import { useMemo } from 'react';

import { ArrowDownIcon, ArrowUpIcon, ExternalLinkIcon } from 'lucide-react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore utils is in the parent dir
import isProd from '@utils/isProd';

import CarrierVerificationCard from 'components/CarrierVerificationCard';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { createTMSInstance } from 'lib/hosts/interface';
import { Maybe } from 'types/UtilityTypes';
import { TMS } from 'types/enums/Integrations';
import { titleCase } from 'utils/formatStrings';

interface OverviewSectionProps {
  tmsName: TMS;
  freightTrackingID: string;
  externalTMSID: string;
  labels: string[];
  loadStatus: string;
  poNumbers: string[];
  fromEmail: Maybe<string>;
  moreThanTwoStops: boolean;
}

export default function OverviewSection({
  tmsName,
  freightTrackingID,
  externalTMSID,
  labels,
  loadStatus,
  poNumbers,
  fromEmail,
  moreThanTwoStops,
}: OverviewSectionProps) {
  const {
    serviceFeaturesEnabled: {
      isCarrierVerificationEnabled,
      isMultiStopLoadViewEnabled,
    },
  } = useServiceFeatures();

  let externalTMSIDMoniker, freightTrackingIDMoniker;

  // TODO: add more tms integration url/support for hyperlink to integration load page
  const integrationLoadPageUrls: Record<TMS | string, string> = {
    [TMS.Turvo]: `https://app.turvo.com/#/SHc59HSy/shipments/${externalTMSID}`,
  };

  try {
    const tmsClient = createTMSInstance(tmsName);
    externalTMSIDMoniker = tmsClient.getFieldMoniker('externalTMSID');
    freightTrackingIDMoniker = tmsClient.getFieldMoniker('freightTrackingID');
  } catch (e) {
    if (!isProd()) console.error(e);
  }

  const externalLinkLabel = useMemo(() => {
    switch (tmsName) {
      case TMS.Turvo:
        return `${titleCase(tmsName)} Shipment`;
      default:
        return `${titleCase(tmsName)} Load`;
    }
  }, [tmsName]);

  return (
    <div className='pb-4 flex flex-col gap-2 w-full text-sm'>
      {externalTMSID && externalTMSIDMoniker && (
        <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
          <h2 className='text-grayscale-content-label text-sm'>
            {externalTMSIDMoniker}
          </h2>
          <span className='px-1 py-0.5 rounded-[4px] border border-orange-border-main bg-orange-bg text-orange-main capitalize text-xs'>
            {externalTMSID}
          </span>
        </div>
      )}

      {freightTrackingID && freightTrackingIDMoniker && (
        <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
          <h2 className='text-grayscale-content-label text-sm'>
            {freightTrackingIDMoniker}
          </h2>
          <span className='px-2 py-1 rounded-[4px] border border-grayscale-border-tag capitalize text-xs'>
            {freightTrackingID}
          </span>
        </div>
      )}

      <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
        <h2 className='text-grayscale-content-label text-sm'>Load Status</h2>
        <span className='px-2 py-1 rounded-[4px] border border-orange-border-main bg-orange-bg text-orange-main capitalize text-xs'>
          {loadStatus ? loadStatus : 'Undetermined'}
        </span>
      </div>

      {isMultiStopLoadViewEnabled && (
        <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
          <h2 className='text-grayscale-content-label text-sm'>Stops</h2>
          <div className='flex items-center gap-2'>
            <TooltipProvider>
              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <span className='flex items-center gap-1 px-2 py-1 rounded-[4px] border border-grayscale-border-tag text-xs'>
                    <span>{moreThanTwoStops ? '2+' : '1'}</span>
                    <ArrowUpIcon size={14} className='text-green-500' />
                  </span>
                </TooltipTrigger>
                <TooltipContent>Number of pickup locations</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip delayDuration={10}>
                <TooltipTrigger asChild>
                  <span className='flex items-center gap-1 px-2 py-1 rounded-[4px] border border-grayscale-border-tag text-xs'>
                    <span>{moreThanTwoStops ? '2+' : '1'}</span>
                    <ArrowDownIcon size={14} className='text-red-500' />
                  </span>
                </TooltipTrigger>
                <TooltipContent>Number of dropoff locations</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      )}

      {poNumbers.length > 0 && (
        <div
          className={`flex flex-row justify-between gap-8 px-4 
            ${poNumbers.length > 2 ? 'items-start' : 'items-center'}`} // align start if PO number render takes up > 1 line
        >
          <h2 className='text-grayscale-content-label text-sm shrink-0'>
            PO #
          </h2>
          <div
            className='flex flex-row flex-wrap justify-end gap-2'
            role='list'
          >
            {poNumbers.map(
              (item, idx) =>
                item &&
                item.trim().length > 0 && (
                  <span
                    role='listitem'
                    key={idx}
                    className='py-1 px-2 rounded-[4px] border border-grayscale-border-tag capitalize text-xs'
                  >
                    {item.trim()}
                  </span>
                )
            )}
          </div>
        </div>
      )}

      {labels.length > 0 && (
        <>
          <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
            <h2 className='text-grayscale-content-label text-sm'>
              Email Labels
            </h2>
            <div
              role='list'
              className='flex flex-row flex-wrap gap-2 justify-end'
            >
              {labels.map(
                (item, idx) =>
                  item.length > 0 && (
                    <span
                      role='listitem'
                      key={idx}
                      className='text-xs py-1 px-2 rounded-[4px] border border-grayscale-border-tag capitalize'
                    >
                      {item.trim()}
                    </span>
                  )
              )}
            </div>
          </div>
        </>
      )}

      {/* Hyper link to integration load page */}
      {tmsName in integrationLoadPageUrls && (
        <div className='flex flex-row flex-wrap gap-2 justify-between px-4 items-center'>
          <p className='text-sm'>{externalLinkLabel}</p>
          <a
            href={integrationLoadPageUrls[tmsName]}
            target='_blank'
            rel='noopener noreferrer'
            className='text-primary text-sm underline gap-1 flex items-center'
          >
            <span className='text-sm'>View in TMS</span>
            <ExternalLinkIcon size={16} />
          </a>
        </div>
      )}

      {isCarrierVerificationEnabled && fromEmail && (
        <CarrierVerificationCard fromEmail={fromEmail} />
      )}
    </div>
  );
}
