import { useEffect, useState } from 'react';

import { Button } from 'components/Button';
import useFetchRecentWarehouses from 'hooks/useFetchRecentWarehouses';
import { useToast } from 'hooks/useToaster';
import { StopTypes } from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';

import AppointmentScheduling from './AppointmentScheduling';
import { ToggleGroup } from 'components/ToggleGroup';
import { ToggleGroupItem } from 'components/ToggleGroup';
import { MiniLoadInfo } from './MiniLoadInfo';

type AppointmentEditorProps = {
  normalizedLoad: NormalizedLoad;
  loadPickupWarehouse: Maybe<Warehouse>;
  loadDropoffWarehouse: Maybe<Warehouse>;
};

export function AppointmentEditor({
  normalizedLoad: load,
  loadPickupWarehouse,
  loadDropoffWarehouse,
}: AppointmentEditorProps) {
  const { toast } = useToast();
  const {
    warehouses: recentWarehouses,
    isLoading,
    error,
  } = useFetchRecentWarehouses();

  const [selectedPickupWarehouse, setSelectedPickupWarehouse] = useState<Maybe<Warehouse>>(loadPickupWarehouse);
  const [selectedDropoffWarehouse, setSelectedDropoffWarehouse] = useState<Maybe<Warehouse>>(loadDropoffWarehouse);

  const [type, setType] = useState<StopTypes>(StopTypes.Pickup);

  // Get the current warehouse and setter based on the selected type
  const getCurrentWarehouse = () => {
    return type === StopTypes.Pickup ? selectedPickupWarehouse : selectedDropoffWarehouse;
  };

  const getCurrentWarehouseSetter = () => {
    return (warehouse: Maybe<Warehouse>) => {
      if (type === StopTypes.Pickup) {
        setSelectedPickupWarehouse(warehouse);
      } else {
        setSelectedDropoffWarehouse(warehouse);
      }
    };
  };

  useEffect(() => {
    if (isLoading) {
      return;
    }
    if (error) {
      toast({
        description: 'Error fetching warehouses.',
        variant: 'destructive',
      });
    }
  }, [isLoading, error]);

  return (
    <div className='flex-1 shrink-0 mt-6'>
      <div className='flex justify-between gap-2 items-center mb-6'>
        <ToggleGroup
          type="single"
          value={type === StopTypes.Pickup ? "Pickup" : "Dropoff"}
          onValueChange={(value) => setType(value === "Dropoff" ? StopTypes.Dropoff : StopTypes.Pickup)}
          className="relative bg-gray-100 rounded-lg p-1 grid w-full grid-cols-2 gap-0 m-0"
        >
          {/* Sliding background */}
          <div
            className={`absolute top-1 bottom-1 w-[calc(50%-4px)] bg-orange-main rounded-md shadow-sm transition-transform duration-200 ease-out ${
              type === StopTypes.Dropoff ? "translate-x-[calc(100%+4px)]" : "translate-x-0"
            }`}
          />

          <ToggleGroupItem
            value="Pickup"
            aria-label="Pickup"
            className="relative z-10 bg-transparent border-0 shadow-none data-[state=on]:bg-transparent data-[state=on]:text-white data-[state=off]:text-gray-500 hover:bg-transparent"
          >
            Pickup
          </ToggleGroupItem>
          <ToggleGroupItem
            value="Dropoff"
            aria-label="Dropoff"
            className="relative z-10 bg-transparent border-0 shadow-none data-[state=on]:bg-transparent data-[state=on]:text-white data-[state=off]:text-gray-500 hover:bg-transparent"
          >
            Dropoff
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <MiniLoadInfo load={load} type={type} />

      <AppointmentScheduling
        type={type}
        load={load}
        recentWarehouses={recentWarehouses}
        selectedWarehouse={getCurrentWarehouse()}
        setSelectedWarehouse={getCurrentWarehouseSetter()}
      />
    </div>
  );
}
