import { useContext, useEffect, useState } from 'react';

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  ArrowLeft,
  Mail,
} from 'lucide-react';

import { Button } from 'components/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from 'components/Card';
import { Textarea } from 'components/Textarea';
import { ServiceContext } from 'contexts/serviceContext';
import { SchedulingPortals, StopTypes } from 'types/Appointment';
import { NormalizedLoad } from 'types/Load';
import { Undef } from 'types/UtilityTypes';
import { Maybe } from 'types/UtilityTypes';
import { Warehouse } from 'types/Warehouse';

import { E2openForm } from './SchedulingPortalForms/E2openForm';
import { OpendockForm } from './SchedulingPortalForms/OpendockForm';
import { RetalixForm } from './SchedulingPortalForms/RetalixForm';
import <PERSON>dockLogo from 'icons/OpendockLogo';
import Retalix<PERSON>ogo from 'icons/RetalixLogo';
import E2openLogo from 'icons/E2OpenLogo';
import OneNetworkLogo from 'icons/OneNetworkLogo';
import ManhattanLogo from 'icons/ManhattanLogo';
import YardviewLogo from 'icons/YardviewLogo';

type Portal = {
  id: string;
  name: string;
  icon: any;
  color: string;
  requiresCustomerInfo?: boolean;
  category?: 'standard' | 'beta';
};

const getPortalConfig = (integrationName: string): Undef<Portal> => {
  const configMap: Partial<Record<SchedulingPortals, Portal>> = {
    [SchedulingPortals.E2open]: {
      id: SchedulingPortals.E2open,
      name: 'E2open',
      icon: E2openLogo,
      color: 'blue',
      category: 'beta',
    },
    [SchedulingPortals.Manhattan]: {
      id: SchedulingPortals.Manhattan,
      name: 'Manhattan',
      icon: ManhattanLogo,
      color: 'blue',
      category: 'beta',
    },
    [SchedulingPortals.OneNetwork]: {
      id: SchedulingPortals.OneNetwork,
      name: 'OneNetwork',
      icon: OneNetworkLogo,
      color: 'red',
      category: 'beta',
    },
    [SchedulingPortals.Opendock]: {
      id: SchedulingPortals.Opendock,
      name: 'Opendock',
      icon: OpendockLogo,
      color: 'green',
      category: 'standard',
    },
    [SchedulingPortals.Retalix]: {
      id: SchedulingPortals.Retalix,
      name: 'Retalix',
      icon: RetalixLogo,
      color: 'green',
      category: 'standard',
    },
    [SchedulingPortals.YardView]: {
      id: SchedulingPortals.YardView,
      name: 'Yardview',
      icon: YardviewLogo,
      color: 'indigo',
      category: 'beta',
    },
  };
  return Object.values(SchedulingPortals).includes(
    integrationName as SchedulingPortals
  )
    ? configMap[integrationName as SchedulingPortals]
    : undefined;
};

// Helper function to get color classes
const getColorClasses = (color: string) => {
  const colorMap: Record<string, { bg: string; text: string }> = {
    blue: { bg: 'bg-blue-100', text: 'text-blue-600' },
    green: { bg: 'bg-green-100', text: 'text-green-600' },
    purple: { bg: 'bg-purple-100', text: 'text-purple-600' },
    indigo: { bg: 'bg-indigo-100', text: 'text-indigo-600' },
    red: { bg: 'bg-red-100', text: 'text-red-600' },
    yellow: { bg: 'bg-yellow-100', text: 'text-yellow-600' },
    pink: { bg: 'bg-pink-100', text: 'text-pink-600' },
    teal: { bg: 'bg-teal-100', text: 'text-teal-600' },
    orange: { bg: 'bg-orange-100', text: 'text-orange-600' },
  };
  return colorMap[color] || { bg: 'bg-gray-100', text: 'text-gray-600' };
};

dayjs.extend(utc);
dayjs.extend(timezone);

type AppointmentSchedulingProps = {
  type: StopTypes;
  load: NormalizedLoad;
  recentWarehouses: Warehouse[];
  selectedWarehouse: Maybe<Warehouse>;
  setSelectedWarehouse: (warehouse: Maybe<Warehouse>) => void;
};

export default function AppointmentScheduling({
  type,
  load,
  recentWarehouses,
  selectedWarehouse,
  setSelectedWarehouse,
}: AppointmentSchedulingProps) {
  const {
    serviceFeaturesEnabled: { isAppointmentEmailingEnabled },
    schedulerIntegrations,
  } = useContext(ServiceContext);

  const [selectedPortal, setSelectedPortal] = useState<Maybe<string>>(selectedWarehouse?.warehouseSource ?? null);
  const [showEmail, setShowEmail] = useState(false);

  const portals: Portal[] = schedulerIntegrations
    .map((integration) => getPortalConfig(integration.name))
    .filter((portal): portal is Portal => portal !== undefined);

  const getPortalsByCategory = () => {
    const standard = portals
      .filter((p) => p.category === 'standard')
    const beta = portals
      .filter((p) => p.category === 'beta')

    return { standard, beta };
  };

  const handlePortalSelect = (portalId: string) => {
    setSelectedPortal(portalId);
  };

  const handleBack = () => {
    if (showEmail) {
      setShowEmail(false);
    } else {
      setSelectedPortal(null);
    }
  };

  const copyEmailTemplate = () => {
    navigator.clipboard.writeText(defaultEmailTemplate);
  };

  if (showEmail) {
    return (
      <div className='w-full mx-auto py-4 space-y-4'>
        <div className='flex items-center space-x-2 mb-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBack}
            className='p-1 hover:border-none'
            buttonNamePosthog={null}
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <h2 className='text-lg font-semibold'>Email Request</h2>
        </div>

        <Card>
          <CardHeader className='pb-3'>
            <CardTitle className='flex items-center space-x-2 text-base'>
              <Mail className='h-4 w-4' />
              <span>Copy & Send</span>
            </CardTitle>
            <CardDescription className='text-xs'>
              Copy this template and send it via email
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-3'>
            <Textarea
              readOnly
              value={defaultEmailTemplate}
              className='min-h-[120px] text-xs font-mono resize-none'
            />
            <Button
              onClick={copyEmailTemplate}
              className='w-full'
              buttonNamePosthog={null}
            >
              Copy Template
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (selectedPortal) {
    const portal = portals.find((p) => p.id === selectedPortal);
    if (!portal) {
      return null;
    }

    const commonProps = {
      type,
      load,
    };

    return (
      <div className='w-full mx-auto py-4 space-y-4'>
        <div className='flex items-center space-x-2 mb-4'>
          <Button
            variant='ghost'
            size='sm'
            onClick={handleBack}
            className='p-1 hover:border-none'
            buttonNamePosthog={null}
          >
            <ArrowLeft className='h-4 w-4' />
          </Button>
          <h2 className='text-lg font-semibold'>{portal?.name}</h2>
        </div>

        <div className='space-y-4'>
          {portal.id === SchedulingPortals.E2open && (
            <E2openForm {...commonProps} />
          )}
          {portal.id === SchedulingPortals.Opendock && (
            <OpendockForm
              {...commonProps}
              recentWarehouses={recentWarehouses.filter(
                (w) => w.warehouseSource === SchedulingPortals.Opendock
              )}
              selectedWarehouse={selectedWarehouse}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
          {portal.id === SchedulingPortals.Retalix && (
            <RetalixForm
              {...commonProps}
              recentWarehouses={recentWarehouses.filter(
                (w) => w.warehouseSource === SchedulingPortals.Retalix
              )}
              selectedWarehouse={selectedWarehouse}
              setSelectedWarehouse={setSelectedWarehouse}
            />
          )}
        </div>
      </div>
    );
  }

  const { standard, beta } = getPortalsByCategory();

  const renderPortalCard = (portal: Portal, isBeta: boolean = false) => {
    const IconComponent = portal.icon;
    const colorClasses = getColorClasses(portal.color);

    return (
      <Card
        key={portal.id}
        className='cursor-pointer transition-all hover:shadow-md hover:bg-gray-50'
        onClick={() => handlePortalSelect(portal.id)}
      >
        <CardContent className='p-2'>
          <div className='flex items-center space-x-3'>
            <div className={`p-2 ${colorClasses.bg} rounded-lg flex-shrink-0`}>
              <IconComponent className={`h-4 w-4 object-contain ${colorClasses.text}`} />
            </div>
            <div className='min-w-0 flex-1'>
              <div className='flex justify-between items-center space-x-2'>
                <h3 className='font-medium text-sm'>{portal.name}</h3>
                {isBeta && (
                  <span className='text-[10px] text-white bg-primary/90 rounded-full px-2 py-0.5'>
                    Beta
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className='w-full mx-auto'>
      <div className='flex items-center space-x-2 mb-4'>
        <h2 className='text-lg mt-5 font-semibold'>Book an Appointment</h2>
      </div>

      {/* Portals Section */}
      <div className='space-y-3'>
        <div className='flex items-center space-x-2'>
          <h3 className='text-sm font-medium text-muted-foreground'>Scheduling Portals</h3>
        </div>

        <div className='space-y-2'>
          {standard.map((portal) => renderPortalCard(portal))}
          {beta.map((portal) => renderPortalCard(portal, true))}
        </div>
      </div>

      {/* Email Request Section */}
      {isAppointmentEmailingEnabled && (
        <div className='pt-4 border-t mt-4'>
          <div className='flex items-center space-x-2 mb-3'>
            <h3 className='text-sm font-medium text-muted-foreground'>Alternative Options</h3>
          </div>

          <Card
            className='cursor-pointer transition-all hover:shadow-md hover:bg-gray-50'
            onClick={() => setShowEmail(true)}
          >
            <CardContent className='p-3'>
              <div className='flex items-start space-x-3'>
                <div className='p-2 bg-orange-100 rounded-lg flex-shrink-0'>
                  <Mail className='h-4 w-4 text-orange-600' />
                </div>
                <div className='min-w-0'>
                  <h3 className='font-medium text-sm'>Email Request</h3>
                  <p className='text-xs text-muted-foreground mt-1'>
                    Get a template to send via email
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

const defaultEmailTemplate = `Subject: Appointment Request

Dear Scheduling Team,

I would like to request an appointment for:
- Company: [Your Company]
- Contact: [Your Name]
- Phone: [Your Phone]
- Preferred Date: [Date]
- Preferred Time: [Time]

Please confirm availability.

Thank you,
[Your Name]`
