import { IntegrationCore } from 'contexts/serviceContext';
import {
  fetchCommodityOptions,
  fetchFreightTransportTypeOptions,
  tridentCommodityOptions,
  tridentTransportTypeOptions,
} from 'pages/QuoteView/LoadBuilding/McleodSectionForms/constants';
import { Undef } from 'types/UtilityTypes';
import captureException from 'utils/captureException';

export const getTransportTypeOptions = (tms: Undef<IntegrationCore>) => {
  if (tms?.tenant.includes('trident')) {
    return tridentTransportTypeOptions;
  }
  if (tms?.tenant.includes('fcfm')) {
    return fetchFreightTransportTypeOptions;
  }

  captureException(
    `No transport type options configured for Mcleod tenant ${tms?.tenant}`,
    {
      tenant: tms?.tenant,
      tmsID: tms?.id,
    }
  );
  return [];
};

export const getCommodityOptions = (tms: Undef<IntegrationCore>) => {
  if (tms?.tenant.includes('trident')) {
    return tridentCommodityOptions;
  }
  if (tms?.tenant.includes('fcfm')) {
    return fetchCommodityOptions;
  }

  captureException(
    `No commodity options configured for Mcleod tenant ${tms?.tenant}`,
    {
      tenant: tms?.tenant,
      tmsID: tms?.id,
    }
  );
  return [];
};
