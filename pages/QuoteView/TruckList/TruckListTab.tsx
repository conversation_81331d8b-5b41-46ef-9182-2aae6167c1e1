import { useEffect, useState } from 'react';

import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { Button } from 'components/Button';
import ButtonLoader from 'components/loading/ButtonLoader';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { toast } from 'hooks/useToaster';
import createTruckList from 'lib/api/createTruckList';
import { TruckListResponse } from 'lib/api/getTruckList';
import { ingestEmail } from 'lib/api/ingestEmail';
import { reprocessEmailContents } from 'lib/api/reprocessEmailContents';
import { Email } from 'types/Email';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { EmailLabels } from 'types/enums/EmailLabels';
import Pageview from 'types/enums/Pageview';

import RedwoodTruckListForm from './Redwood/RedwoodTruckListForm';
import {
  getToastForIngestionTriggerFailed,
  getToastForIngestionTriggerInfo,
} from './Redwood/RedwoodUtils';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

export enum TruckType {
  VAN = 'VAN',
  REEFER = 'REEFER',
  FLATBED = 'FLATBED',
}

type TruckListSectionProps = {
  email: Maybe<Email>;
  truckList: MaybeUndef<TruckListResponse>;
};

export default function TruckListTab({
  email,
  truckList,
}: TruckListSectionProps) {
  useLogPostHogPageView(Pageview.TruckList);

  const [isIngesting, setIsIngesting] = useState(false);
  const [isLoadingNewTruckList, setIsLoadingNewTruckList] = useState(false);
  const [truckListFormData, setTruckListFormData] = useState(truckList);
  const [isIngestedEmail, setIsIngestedEmail] = useState(false);

  useEffect(() => {
    // All emails from Redwood are processed as truck lists, so if we have an email
    // and no truck list that means we already failed to extract one from its contents.
    //
    // We only show the manual ingest button if we don't have an email, since that
    // means Drumkit hasn't attempted to extract it from the email's content yet.
    setIsIngestedEmail(Boolean(email?.id));
  }, []);

  useEffect(() => {
    setTruckListFormData(truckList);
  }, [truckList]);

  const updateTruckListForm = ({
    trucks,
    carrier,
    errors,
  }: Pick<TruckListResponse, 'trucks' | 'carrier' | 'errors'>) => {
    if (!truckListFormData) return;

    setTruckListFormData((prevTruckList) => ({
      trucks,
      carrier,
      errors: errors ?? undefined,
      serviceName: prevTruckList?.serviceName ?? '',
    }));
  };

  const handleCreateTruckList = async () => {
    if (!email || !email.threadID) return;

    setIsLoadingNewTruckList(true);
    const res = await createTruckList(email.id, email.threadID);

    if (res.isOk()) {
      toast({ title: 'Truck List created', variant: 'success' });

      setTruckListFormData(res.value);
    } else {
      toast({ title: 'Error creating truck list', variant: 'destructive' });
    }

    setIsLoadingNewTruckList(false);
  };

  const handleTriggerManualIngest = async () => {
    let res;

    setIsIngesting(true);
    toast(getToastForIngestionTriggerInfo()); // Inform user that email is being processed and page will refresh

    if (email?.id) {
      res = await reprocessEmailContents(email.id, [EmailLabels.TruckList]);
    } else if (email?.threadID) {
      res = await ingestEmail(email.threadID);
    }

    if (res?.isOk()) {
      window.location.reload(); // Refresh page to show processed email
    } else if (res?.isErr()) {
      toast(getToastForIngestionTriggerFailed(res.error.message));
    }

    setIsIngesting(false);
  };

  return truckListFormData ? (
    <RedwoodTruckListForm
      email={email}
      truckList={truckListFormData}
      updateTruckListForm={updateTruckListForm}
    />
  ) : (
    <>
      <p className='my-4 text-center'>
        {isIngestedEmail
          ? 'Drumkit processed this email successfully but no trucks were found'
          : "Drumkit didn't process this email since it wasn't identified as a truck list"}
      </p>

      <Button
        className='w-full my-4'
        buttonNamePosthog={ButtonNamePosthog.IngestEmail}
        onClick={handleTriggerManualIngest}
        type='button'
        disabled={isIngesting || isLoadingNewTruckList}
      >
        {isIngesting ? <ButtonLoader /> : <span>{ButtonText.IngestEmail}</span>}
      </Button>

      {isIngestedEmail && (
        <>
          <p className='text-center'>OR</p>
          <Button
            className='w-full my-4'
            buttonNamePosthog={ButtonNamePosthog.CreateTruckList}
            onClick={handleCreateTruckList}
            type='button'
            disabled={isIngesting || isLoadingNewTruckList}
          >
            {isLoadingNewTruckList ? (
              <ButtonLoader />
            ) : (
              <span>Create Truck List</span>
            )}
          </Button>
        </>
      )}
    </>
  );
}
