import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
  Controller,
  FormProvider,
  SubmitHandler,
  useForm,
} from 'react-hook-form';

import { Divider } from 'antd';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore lodash is in the parent dir
import _ from 'lodash';
import {
  ArrowRightIcon,
  CheckIcon,
  CopyIcon,
  InfoIcon,
  MapIcon,
} from 'lucide-react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore mustache is in the parent dir
import Mustache from 'mustache';

import { AddDateDetailsButton } from 'components/AddDateDetailsButton';
import { Button } from 'components/Button';
import { DatePicker } from 'components/DatePicker';
import { GenericLineChart } from 'components/GenericLineChart';
import { Label } from 'components/Label';
import { QuoteCard, QuoteCardType } from 'components/QuoteCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from 'components/Select';
import { Textarea } from 'components/Textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from 'components/Tooltip';
import { Input } from 'components/input';
import { RHFDebounceSelect } from 'components/input/RHFDebounceSelect';
import { RHFTextInput } from 'components/input/RHFTextInput';
import ButtonLoader from 'components/loading/ButtonLoader';
import CarrierPriceCalculator, {
  CarrierPriceCalculatorParent,
} from 'components/pricing/CarrierPriceCalculator';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import {
  DrumkitPlatform,
  SidebarStateContext,
  isEmailPlatform,
} from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { useToast } from 'hooks/useToaster';
import DATLogo from 'icons/DAT';
import InfoCircleIcon from 'icons/InfoCircle';
import { getCustomers } from 'lib/api/getCustomers';
import {
  LaneHistoryResponse,
  SourceHistory,
  WeekLaneData,
} from 'lib/api/getLaneHistory';
import {
  QuickQuoteInputs,
  QuickQuoteResponse,
  SelectedCarrierType,
  getQuickQuote,
} from 'lib/api/getQuickQuote';
import { getUserDATInfo } from 'lib/api/getUserDATInfo';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import { getCurrentTab, getTabHTML } from 'lib/chromeScripting/util';
import { QuotingPortal, newQuotingPortal } from 'lib/hosts/quoting/interface';
import { createMailClientInstance } from 'lib/mailclient/interface';
import { Email } from 'types/Email';
import { TMSCustomer } from 'types/Load';
import { TransportType } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import { SubmitQuoteToPortalMessage } from 'types/chromescript/QuotingPortal';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import {
  Quoting,
  QuotingPortals,
  TMS,
  integrationNameMap,
} from 'types/enums/Integrations';
import { SuggestionPipelines } from 'types/suggestions/CoreSuggestions';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import {
  QuoteChanges,
  SuggestedQuoteChange,
} from 'types/suggestions/QuoteSuggestions';
import captureException from 'utils/captureException';
import convertZipPlus4ToRegular from 'utils/convertExtendedZipToRegular';
import { copyToClipboard } from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { titleCase } from 'utils/formatStrings';
import {
  chartConfig,
  chartConfigPercentile,
} from 'utils/laneHistoryChartConfig';
import {
  GenericCompanySearchableFields,
  customerSearchHandler,
  mapCustomerToAntdOptions,
} from 'utils/loadInfoAndBuilding';
import { calculatePricing } from 'utils/priceCalculations';
import { cn } from 'utils/shadcn';

import { useHelperFunctions } from './helperFunctions';
import {
  CarrierCostType,
  LaneTier,
  ProfitType,
  QuickQuoteTextInputProps,
  TransportTypeOption,
  getTransportTypeOptions,
} from './types';

export const QuickQuoteTextInput = (props: QuickQuoteTextInputProps) => (
  <RHFTextInput {...props} />
);

export enum FuelType {
  DOE = 'DOE',
  DAT = 'DAT',
  Portal = 'Portal', // e.g. Freightview, E2Open, etc.
  None = 'None', // Handles initialization logic
}

export type DistanceSource = Quoting | QuotingPortals;

export default function QuickQuoteForm({ email }: { email: Maybe<Email> }) {
  const { toast } = useToast();
  const [customers, setCustomers] = useState<Maybe<TMSCustomer[]>>(null);
  const [initialCustomers, setInitialCustomers] =
    useState<Maybe<TMSCustomer[]>>(null);
  const [quote, setQuote] = useState<Maybe<QuickQuoteResponse>>(null);
  const [hasThirdPartyQuoteURLs, setHasThirdPartyQuoteURLs] =
    useState<boolean>(false);

  const [laneHistory, setLaneHistory] =
    useState<Maybe<LaneHistoryResponse>>(null);
  const [isLoadingLaneHistory, setIsLoadingLaneHistory] = useState(false);
  const [tmsLaneHistory, setTMSLaneHistory] =
    useState<Maybe<SourceHistory[]>>(null);
  const [tmsLaneHistorySelectedTierIndex, setTmsLaneHistorySelectedTierIndex] =
    useState<Maybe<number>>(null);

  const [quoteNotConfident, setQuoteNotConfident] = useState<boolean>(false);
  const [selectedCarrier, setSelectedCarrier] = useState<SelectedCarrierType>(
    SelectedCarrierType.NETWORK
  );
  const [quoteCards, setQuoteCards] = useState<QuoteCardType[]>([]);
  const [carrierCost, setCarrierCost] = useState(0);
  const [carrierCostType, setCarrierCostType] = useState<CarrierCostType>(
    CarrierCostType.Flat
  );
  const [maxDistance, setMaxDistance] = useState(0);
  const [distanceSource, setDistanceSource] =
    useState<Maybe<DistanceSource>>(null);

  const {
    serviceFeaturesEnabled: {
      isTMSQuoteSubmissionEnabled,
      isQuoteLaneHistoryEnabled,
      isTMSLaneHistoryEnabled,
      isQuoteSubmissionViaURLEnabled,
      isQuoteSubmissionToServiceEnabled,
      isGetLaneRateFromServiceEnabled,
    },
    tmsIntegrations,
    quotingIntegrations,
    quickQuoteConfig,
    serviceID,
  } = useServiceFeatures();

  // Initialize with sensible defaults, will be updated by useEffect when config loads
  // TODO: Rename quickQuotConfig defaultMargin to defaultProfit
  const [profitType, setProfitType] = useState<ProfitType>(
    quickQuoteConfig?.defaultMarginType
      ? (quickQuoteConfig?.defaultMarginType as ProfitType)
      : ProfitType.Percentage
  );

  // TODO: Rename quickQuoteConfig defaultMargin to defaultProfit
  const [profit, setProfit] = useState(
    profitType === ProfitType.Amount
      ? quickQuoteConfig?.defaultFlatMargin || 100
      : quickQuoteConfig?.defaultPercentMargin || 10
  );

  const [finalPrice, setFinalPrice] = useState<Maybe<number>>(null);

  // If carrier cost is per mile, finalFlatPrice is calculated as (cost + margin) * distance
  const [finalFlatPrice, setFinalFlatPrice] = useState(finalPrice);
  const [finalPriceFormat, setFinalPriceFormat] = useState<
    CarrierCostType | 'Both'
  >('Both');

  // Final flat price minus fuel surcharge
  const [fuelEstimate, setFuelEstimate] = useState(0);
  const [datFuelSurcharge, setDATFuelSurcharge] = useState<Maybe<number>>(null);

  const [draftResponse, setDraftResponse] = useState('');
  const [userEditedDraft, setUserEditedDraft] = useState(false);
  const [hasCopiedDraftResponse, setHasCopiedDraftResponse] = useState(false);
  // If user edits message but wants to toggle the format of the price,
  // we need to keep track of the current price string to replace it with the new one
  const [currentPriceString, setCurrentPriceString] = useState('');

  const [loadingDraftReply, setLoadingDraftReply] = useState(false);
  const [isSubmitToTMS, setIsSubmitToTMS] = useState<boolean>(true);
  const [createdQuoteId, setCreatedQuoteId] = useState<Maybe<number>>();

  // This is the ID of the parent quote request that we reference after calling GetQuickQuote. It is used to
  // update the quote request to accepted at the end of the quick quote flow.
  const [parentQuoteRequestId, setParentQuoteRequestId] = useState<number>(0);

  const [tmsTenant, setTMSTenant] = useState<Maybe<string>>();

  const [greenscreensQuoteID, setGreenscreensQuoteID] = useState<string>('');

  const [serviceUsesDAT, setServiceUsesDAT] = useState<boolean>(false);
  const [isLoadingDAT, setIsLoadingDAT] = useState<boolean>(false);
  const [datEmailAddress, setDATEmailAddress] = useState<string>('');
  const [hasGrantedDATPermissions, setHasGrantedDATPermissions] =
    useState<Maybe<boolean>>(null);

  const [hasAddedQuoteDates, setHasAddedQuoteDates] = useState<boolean>(false);
  const scrollResultsIntoViewRef = useRef<HTMLDivElement>(null);

  const [submitQuoteLoading, setSubmitQuoteLoading] = useState<boolean>(false);
  const [isSubmittingToPortal, setIsSubmittingToPortal] = useState(false);

  const {
    currentState: {
      drumkitPlatform,
      threadItemId,
      isOutlookReply,
      clickedSuggestion,
      goToSuggestionInCarousel,
      getDisplayedSuggestion,
      curSuggestionList,
      tabId,
    },
    setCurrentState,
  } = useContext(SidebarStateContext);

  const [portal, setPortal] = useState<Undef<QuotingPortal>>(undefined);
  const [showSubmitToPortalButtons, setShowSubmitToPortalButtons] =
    useState<boolean>(false);

  const transportTypeOptions = useMemo(
    () => getTransportTypeOptions(quickQuoteConfig),
    [quickQuoteConfig]
  );

  // Properties for GetQuickQuote button Posthog logging
  const getQuickQuoteProperties = useMemo(() => {
    return {
      serviceID,
      hasQRSuggestions: curSuggestionList.some(
        (s) => s.pipeline === SuggestionPipelines.QuickQuote
      ),
      profitType,
      carrierCostType,
      suggestionId: clickedSuggestion?.id,
    };
  }, [
    serviceID,
    curSuggestionList,
    profitType,
    carrierCostType,
    clickedSuggestion,
  ]);

  // Properties for CopyQuoteToClipboard button Posthog logging
  const copyToClipboardProperties = useMemo(() => {
    if (!quote && !quoteNotConfident) return {};

    const { flatCarrierCost, finalProfit } = calculatePricing(
      carrierCost,
      carrierCostType,
      profit,
      profitType,
      maxDistance
    );

    return {
      serviceID,
      parentQuoteRequestId,
      finalQuotePrice: finalFlatPrice ?? 0,
      finalMargin: finalProfit,
      marginType: profitType,
      finalCarrierCost: flatCarrierCost,
      carrierCostType: carrierCostType,
      hasQRSuggestions: curSuggestionList.some(
        (s) => s.pipeline === SuggestionPipelines.QuickQuote
      ),
      suggestionId: clickedSuggestion?.id,
    };
  }, [
    quote,
    quoteNotConfident,
    carrierCost,
    carrierCostType,
    profit,
    profitType,
    maxDistance,
    serviceID,
    parentQuoteRequestId,
    finalFlatPrice,
    curSuggestionList,
    clickedSuggestion,
  ]);

  useEffect(() => {
    async function checkTab() {
      const tab = await getCurrentTab();
      if (!tab?.id) return;

      const portal = newQuotingPortal(tab?.url);

      setPortal(portal);
      setShowSubmitToPortalButtons(!!portal);
    }
    checkTab();
  }, []);

  const isTaiOrGlobalTranzTMS = tmsIntegrations?.some(
    (tms) => tms.name === TMS.Tai || tms.name === TMS.GlobalTranzTMS
  );

  const fetchUserDATInfo = async () => {
    const res = await getUserDATInfo();
    if (res.isOk()) {
      setHasGrantedDATPermissions(res.value.hasGrantedDATPermissions);
    }
  };

  useEffect(() => {
    const serviceHasDATIntegration = quotingIntegrations.some(
      (qIntegration) => qIntegration.name === Quoting.DAT
    );

    if (serviceHasDATIntegration) {
      setServiceUsesDAT(true);
    }
  }, [quotingIntegrations]);

  useEffect(() => {
    if (serviceUsesDAT) {
      fetchUserDATInfo();
    }
  }, [serviceUsesDAT]);

  const memoizedDefaultValues = useMemo<QuickQuoteInputs>(() => {
    const suggestedFields = clickedSuggestion?.suggested as QuoteChanges;

    // Process suggested pickup location
    const pickupLocation = useHelperFunctions.parseLocation(
      suggestedFields?.pickupZip
        ? suggestedFields.pickupZip
        : `${suggestedFields?.pickupCity}, ${suggestedFields?.pickupState}`
    );

    // Process suggested delivery location
    const deliveryLocation = useHelperFunctions.parseLocation(
      suggestedFields?.deliveryZip
        ? suggestedFields.deliveryZip
        : `${suggestedFields?.deliveryCity}, ${suggestedFields?.deliveryState}`
    );

    const parsedValues = {
      transportType: suggestedFields?.transportType ?? '',
      customerName: suggestedFields?.customerExternalTMSID ?? '',
      pickupDate: suggestedFields?.pickupDate,
      deliveryDate: suggestedFields?.deliveryDate,
      stops: [
        {
          location: pickupLocation
            ? pickupLocation.zip
              ? pickupLocation.zip
              : `${pickupLocation.city}, ${pickupLocation.state}`
            : '',
          city: pickupLocation?.city ?? '',
          state: pickupLocation?.state ?? '',
          zip: pickupLocation?.zip ?? '',
          country: pickupLocation?.country ?? '',
        },
        {
          location: deliveryLocation
            ? deliveryLocation.zip
              ? deliveryLocation.zip
              : `${deliveryLocation.city}, ${deliveryLocation.state}`
            : '',
          city: deliveryLocation?.city ?? '',
          state: deliveryLocation?.state ?? '',
          zip: deliveryLocation?.zip ?? '',
          country: pickupLocation?.country ?? '',
        },
      ],
      isSubmitToTMS: true,
    };

    if (
      tmsIntegrations &&
      tmsIntegrations.length > 0 &&
      !isTaiOrGlobalTranzTMS
    ) {
      useHelperFunctions.fetchCustomers({
        setInitialCustomers,
        setCustomers,
        setTMSTenant,
        tmsIntegrations,
      });
    }

    return parsedValues as QuickQuoteInputs;
  }, [clickedSuggestion]);

  const formMethods = useForm<QuickQuoteInputs>({
    defaultValues: memoizedDefaultValues,
  });

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    setValue,
    setError,
    formState: { errors, isSubmitting },
  } = formMethods;

  const handleRefreshCustomers = async () => {
    const res = await getCustomers(tmsIntegrations?.[0]?.id, true);
    if (res.isOk()) {
      setInitialCustomers(res.value.customerList);
      setCustomers(res.value.customerList);
      toast({
        description: 'Successfully refreshed customer list.',
        variant: 'success',
      });
    } else {
      toast({
        description: 'Error while refreshing customer list.',
        variant: 'destructive',
      });
    }
  };

  const handleResetCustomerSearch = () => {
    setCustomers(initialCustomers);
  };

  const handleCustomerSearch = async (
    field: GenericCompanySearchableFields,
    value: string
  ) => {
    return customerSearchHandler({
      tmsID: tmsIntegrations?.[0]?.id,
      customers,
      setCustomers,
      field,
      value,
    });
  };

  const formatDraftTemplate = (
    templateBody: string,
    pickupCity: string,
    pickupState: string,
    deliveryCity: string,
    deliveryState: string,
    pickupDate: Date | undefined,
    deliveryDate: Date | undefined,
    rate: string
  ) => {
    const transportType = getValues('transportType');
    const pickupLocation = `${useHelperFunctions.toTitleCase(pickupCity || '')}, ${pickupState || ''}`;
    const deliveryLocation = `${useHelperFunctions.toTitleCase(deliveryCity || '')}, ${deliveryState || ''}`;

    if (pickupDate && isNaN(pickupDate.getTime())) {
      pickupDate = undefined;
    }

    if (deliveryDate && isNaN(deliveryDate.getTime())) {
      deliveryDate = undefined;
    }

    return Mustache.render(templateBody, {
      pickupLocation,
      deliveryLocation,
      transportType: titleCase(transportType),
      pickupDate: pickupDate?.toDateString(),
      deliveryDate: deliveryDate?.toDateString(),
      rate: rate,
    });
  };

  const formatFinalPriceInDraft = (
    flatPrice: Maybe<number>,
    format: CarrierCostType | 'Both',
    fuelEstimate?: number
  ): string => {
    const price = flatPrice ?? 0;
    if (format === CarrierCostType.Linehaul && fuelEstimate) {
      return `${formatCurrency(price - fuelEstimate, 'USD')}`;
    }

    if (!maxDistance) return formatCurrency(price, 'USD');

    if (format === 'Both') {
      return `${formatCurrency(price, 'USD')} (${formatCurrency(
        price / maxDistance,
        'USD',
        2
      )}/mile)`;
    } else if (format === CarrierCostType.PerMile) {
      return `${formatCurrency(price / maxDistance, 'USD', 2)}/mile`;
    } else {
      return formatCurrency(price, 'USD');
    }
  };

  // Setting default quote card
  useEffect(() => {
    if (!quoteCards?.length) return;

    // If TMS lane history is enabled, always select that box
    if (
      isTMSLaneHistoryEnabled &&
      laneHistory &&
      Object.values(laneHistory?.resultsBySource)?.length > 0
    ) {
      const laneHistoryCard = quoteCards.find(
        (c) => c.type === SelectedCarrierType.LANE_HISTORY
      );
      if (laneHistoryCard) {
        setSelectedCarrier(SelectedCarrierType.LANE_HISTORY);
        setCarrierCost(_.round(laneHistoryCard.cost, 2));
        return;
      }
    }

    const hasDATCarrier = quoteCards.find(
      (c) => c.type === SelectedCarrierType.DAT
    );
    const defaultCarrier =
      hasDATCarrier ??
      quoteCards.reduce((prev, curr) => (prev.cost < curr.cost ? prev : curr));

    setSelectedCarrier(defaultCarrier.type);
    setCarrierCost(_.round(defaultCarrier.cost));
  }, [quoteCards]);

  const handleEnableDATAccess = async () => {
    setIsLoadingDAT(true);

    await useHelperFunctions.enableDATIndividualAccess({
      datEmailAddress,
      setHasGrantedDATPermissions,
    });

    setIsLoadingDAT(false);
  };

  const getSelectedQuickQuoteId = (): number | undefined => {
    if (!quote || !quote.quotes || !selectedCarrier) {
      return undefined;
    }

    // Type for elements in quote.quotes (respQuotes) has source: Quoting
    // but we might encounter 'tms_lane_history' as a string if not in Quoting enum.
    let selectedQuoteMatcher: (q: {
      id: number;
      source: string;
      [key: string]: any;
    }) => boolean;

    switch (selectedCarrier) {
      case SelectedCarrierType.DAT:
        selectedQuoteMatcher = (q) => q.source === Quoting.DAT;
        break;
      case SelectedCarrierType.LANE_HISTORY:
        // Compare source as string to handle 'tms_lane_history' if it's not in Quoting enum
        selectedQuoteMatcher = (q) =>
          (q.source as string) === 'tms_lane_history';
        break;
      case SelectedCarrierType.NETWORK: // Typically Greenscreens Network
      case SelectedCarrierType.BUYPOWER: // Typically Greenscreens BuyPower
        selectedQuoteMatcher = (q) => q.source === Quoting.Greenscreens;
        break;
      case SelectedCarrierType.TRUCKSTOP_POSTED:
      case SelectedCarrierType.TRUCKSTOP_BOOKED:
        selectedQuoteMatcher = (q) => q.source === Quoting.TruckStop;
        break;
      default:
        // If selectedCarrier doesn't map to a known quick quote source we track.
        return undefined;
    }
    const foundQuote = quote.quotes.find(selectedQuoteMatcher);
    return foundQuote?.id;
  };

  const onSubmitForm: SubmitHandler<QuickQuoteInputs> = async (formValues) => {
    setCarrierCostType(CarrierCostType.Flat);
    setLaneHistory(null);

    // Check for ZIP+4 inputs and convert to regular ZIP if necessary
    convertZipPlus4ToRegular({ formValues, setValue });

    const currentSelectedQuickQuoteId = getSelectedQuickQuoteId();

    await useHelperFunctions.onSubmitForm({
      getValues,
      formValues,
      setIsSubmitToTMS,
      setCreatedQuoteId,
      setQuote,
      setQuoteCards,
      isQuoteSubmissionViaURLEnabled,
      email,
      setHasThirdPartyQuoteURLs,
      setValue,
      isGetLaneRateFromServiceEnabled,
      clickedSuggestion,
      formMethods,
      setQuoteNotConfident,
      getQuickQuote,
      isQuoteLaneHistoryEnabled,
      isTMSLaneHistoryEnabled,
      setIsLoadingLaneHistory,
      setLaneHistory,
      setCarrierCost,
      setProfit,
      profitType,
      setError,
      setParentQuoteRequestId,
      setGreenscreensQuoteID,
      isQuoteSubmissionToServiceEnabled,
      setDATFuelSurcharge,
      selectedQuickQuoteId: currentSelectedQuickQuoteId,
    });
  };

  useEffect(() => {
    // If TMS lane history is enabled, set the selected lane tier to the first tier in the list
    const tmsHistoriesKey = Object.keys(
      laneHistory?.resultsBySource || {}
    ).find((source) => Object.values(TMS).includes(source as TMS));

    if (tmsHistoriesKey) {
      setTMSLaneHistory(
        laneHistory?.resultsBySource[tmsHistoriesKey as TMS] || null
      );
      setTmsLaneHistorySelectedTierIndex(0);
    }
  }, [laneHistory]);

  const toggleLaneHistoryGraph = (option: string) => {
    if (!tmsLaneHistory) return;

    const tierIndex = tmsLaneHistory?.findIndex(
      (history) => history.laneTier === option
    );
    if (tierIndex === -1) return;

    setTmsLaneHistorySelectedTierIndex(tierIndex);
  };

  // If no suggestions have been applied and there are QQ ones, apply the first one
  useEffect(() => {
    const firstQuickQuoteSuggestion = curSuggestionList.find(
      (s) => s.pipeline === SuggestionPipelines.QuickQuote
    );

    // Update clickedSuggestion whenever curSuggestionList changes and there's a QuickQuote suggestion
    if (
      firstQuickQuoteSuggestion &&
      clickedSuggestion !== firstQuickQuoteSuggestion
    ) {
      setCurrentState((prevState) => ({
        ...prevState,
        clickedSuggestion: firstQuickQuoteSuggestion,
      }));
    }
  }, [curSuggestionList]);

  // If applied suggestion is QQ, highlight it in carousel
  useEffect(() => {
    const displayedSuggestion = getDisplayedSuggestion();
    if (
      clickedSuggestion &&
      clickedSuggestion.pipeline === SuggestionPipelines.QuickQuote &&
      displayedSuggestion?.id !== clickedSuggestion?.id
    ) {
      goToSuggestionInCarousel({
        suggestionID: clickedSuggestion?.id,
      });
    }
  }, [clickedSuggestion, goToSuggestionInCarousel]);

  const handleCopyToClipboard = async () => {
    try {
      const success = await copyToClipboard(draftResponse);
      if (success) {
        setHasCopiedDraftResponse(true);
        handleTerminatingAction();
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedDraftResponse(false), 2000);
      }
    } catch (error) {
      captureException(error, { functionName: 'handleCopyToClipboard' });

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  // Update draft response when quote, finalPrice or finalPriceFormat changes.
  // If user edited the draft, then replace just the price substring, not all of their changes.
  useEffect(() => {
    let pickupCity, pickupState, deliveryCity, deliveryState;
    const pickupDate = new Date(getValues('pickupDate'));
    const deliveryDate = new Date(getValues('deliveryDate'));

    if (!quote) {
      pickupCity = getValues('stops.0.city');
      pickupState = getValues('stops.0.state');
      deliveryCity = getValues('stops.1.city');
      deliveryState = getValues('stops.1.state');
    } else {
      pickupCity = quote.stops[0].city;
      pickupState = quote.stops[0].state;
      deliveryCity = quote.stops[1].city;
      deliveryState = quote.stops[1].state;
    }

    const finalPrice = formatFinalPriceInDraft(
      finalFlatPrice,
      finalPriceFormat,
      fuelEstimate
    );

    // Get the template body
    const templateBody = quote?.quoteReplyDraftTemplate?.body || '';

    // Get template draft
    const templateDraft = formatDraftTemplate(
      templateBody,
      pickupCity,
      pickupState,
      deliveryCity,
      deliveryState,
      pickupDate,
      deliveryDate,
      finalPrice
    );

    // Assume the template is the final draft
    let finalDraft = templateDraft;

    // If draft exists and user didn't edit (or did edit but kept the price), only update price
    const onlyUpdateDraftPrice =
      draftResponse &&
      (!userEditedDraft || draftResponse.includes(currentPriceString));

    if (onlyUpdateDraftPrice) {
      finalDraft = draftResponse.replace(currentPriceString, finalPrice);
    }

    setDraftResponse(finalDraft);
    setCurrentPriceString(finalPrice);
  }, [quote, finalFlatPrice, finalPriceFormat]);

  // Update form values when suggestion changes
  useEffect(() => {
    if (memoizedDefaultValues) {
      reset(memoizedDefaultValues);
    }
    // Use setValue so as to not show AI-filled helper text
    if (!getValues('transportType')) {
      if (quickQuoteConfig && quickQuoteConfig.defaultTransportType) {
        setValue(
          'transportType',
          quickQuoteConfig.defaultTransportType.toUpperCase() as TransportType
        );
      } else {
        setValue('transportType', TransportType.VAN);
      }
    }
  }, [memoizedDefaultValues, quickQuoteConfig]);

  useEffect(() => {
    if (quote || quoteNotConfident) {
      if (
        clickedSuggestion?.pipeline === SuggestionPipelines.QuickQuote &&
        clickedSuggestion?.suggested.distanceMiles
      ) {
        setMaxDistance(clickedSuggestion.suggested.distanceMiles);
        setDistanceSource(clickedSuggestion.source);
      } else if (quote?.quotes?.length) {
        const maxDistanceQuote = quote.quotes.reduce((max, current) =>
          current.distance > max.distance ? current : max
        );
        setMaxDistance(maxDistanceQuote.distance);
        setDistanceSource(maxDistanceQuote.source as DistanceSource);
      }
    }
  }, [quote, quoteNotConfident, clickedSuggestion]);

  useEffect(() => {
    if (carrierCostType === CarrierCostType.Flat) {
      setFinalFlatPrice(finalPrice);
    } else {
      setFinalFlatPrice((finalPrice ?? 0) * maxDistance);
    }
  }, [finalPrice, carrierCostType, maxDistance]);

  /* handleTerminatingAction should be called whenever user takes an action that "terminates" the quote request,
   * i.e. updates the QR status from "inFlight" to 1 of 2 terminating statuses: "accepted" or "rejected".
   * As of 6/4/2025, this occurs when user clicks "Submit to Portal", "Submit to TMS," "Create Draft Reply",
   * or one of the copy buttons.
   * This function
   * 1) Updates BE quote request with final quote and margin values (critical for metrics)
   * 2) Sends user quote to service (if enabled)
   * 3) Updates UI to show success or failure toast
   * TODO: Capture line haul in BE
   */
  const handleTerminatingAction = async () => {
    // Use the utility function for final price and margin calculations
    const { flatCarrierCost: finalFlatCarrierCost, finalProfit } =
      calculatePricing(
        carrierCost,
        carrierCostType,
        profit,
        profitType,
        maxDistance
      );

    if (isQuoteSubmissionToServiceEnabled) {
      useHelperFunctions.sendUserQuote({
        email,
        quote,
        parentQuoteRequestId,
        greenscreensQuoteID,
        carrierCost: _.round(finalFlatCarrierCost),
        profit: _.round(finalProfit),
        finalPrice: finalFlatPrice ?? 0,
        draftResponse,
        profitType,
      });
    }

    // TODO: Remove marginType and use profitType instead
    const currentSelectedQuickQuoteId = getSelectedQuickQuoteId();
    const result = await updateQuoteRequestSuggestion(
      parentQuoteRequestId,
      SuggestionStatus.Accepted,
      {
        finalQuotePrice: _.round(finalFlatPrice ?? 0),
        finalMargin: _.round(finalProfit),
        marginType: profitType,
        finalCarrierCost: _.round(finalFlatCarrierCost),
        carrierCostType: carrierCostType,
        customerExternalTMSId: getValues('customerName'),
      },
      currentSelectedQuickQuoteId
    );

    if (result.isErr()) {
      captureException(result.error, {
        functionName: 'handleTerminatingAction',
      });
    }
  };

  const handleSubmitQuoteToTMS = async () => {
    if (isTMSQuoteSubmissionEnabled && isSubmitToTMS) {
      const customerId = getValues('customerName');

      if (!customerId) {
        toast({
          description: 'Please enter a customer name.',
          variant: 'destructive',
        });
        return;
      }

      setSubmitQuoteLoading(true);

      const createdQuote = await useHelperFunctions.processQuoteTMSSubmission({
        customerId: customerId,
        finalPrice: finalFlatPrice,
        getValues,
      });

      createdQuote?.quoteId && setCreatedQuoteId(createdQuote.quoteId);
      setSubmitQuoteLoading(false);
    }
  };

  let handleDraftResponse: () => void;
  if (drumkitPlatform === DrumkitPlatform.Outlook) {
    // TODO: Warn user to configure signature in Portal if none found
    const mailClient = createMailClientInstance(drumkitPlatform);
    handleDraftResponse = async () => {
      if (!finalFlatPrice || finalFlatPrice <= 0) {
        return;
      }

      setLoadingDraftReply(true);

      try {
        // Convert newlines to HTML <br> tags since the mail client works with HTML.
        const formattedDraftBody = draftResponse.trim().replace(/\n/g, '<br>');

        await mailClient.draftReply({
          threadItemId,
          draftBody: formattedDraftBody,
        });

        handleTerminatingAction();

        // only remove suggestion from list at the very end of a successful draft creation
        if (clickedSuggestion) {
          setCurrentState((prevState) => ({
            ...prevState,
            clickedSuggestion: null,
            curSuggestionList: prevState.curSuggestionList.filter(
              (s) => s.id !== clickedSuggestion.id
            ),
          }));
        }

        // When in Read View, there's a lag between when Outlook created the draft in the backend
        // and showing it in the client so wait for a moment before showing toaster.
        setTimeout(
          () => {
            toast({
              description: 'Successfully created draft reply.',
              variant: 'success',
            });

            setLoadingDraftReply(false);
          },
          isOutlookReply ? 1 : 3500
        );
      } catch (error: unknown) {
        captureException(error, { functionName: 'handleDraftResponse' });

        toast({
          description: 'Something went wrong creating draft reply',
          variant: 'destructive',
        });

        setLoadingDraftReply(false);
      }
    };
  }

  const constructGoogleMapsUrl = (stops: { city: string; state: string }[]) => {
    const origin = encodeURIComponent(stops[0].city + ',' + stops[0].state);
    const waypoints = stops
      .slice(1, -1)
      .map(
        (stop) =>
          `&waypoints=${encodeURIComponent(stop.city + ',' + stop.state)}`
      )
      .join('');
    const destination = encodeURIComponent(
      stops[stops.length - 1].city + ',' + stops[stops.length - 1].state
    );

    return `https://www.google.com/maps/dir/?api=1&origin=${origin}${waypoints}&destination=${destination}`;
  };

  // Submit to Portal button, only if on an integrated portal
  // `quote` param may be total sell or line haul quote, depending on which button is clicked
  const handleSubmitToPortal = async (quote: Maybe<number>) => {
    if (!portal) {
      captureException(
        'Submit to Portal button clicked but not on a supported portal',
        {
          functionName: 'handleSubmitToPortal',
        }
      );

      toast({
        description: 'Not on a supported portal.',
        variant: 'warning',
      });
      return;
    }

    if (
      // NOTE: Button disabled attribute not used because webpage may change without sidepanel change
      !portal.canSubmit(await getCurrentTab(), (await getTabHTML(tabId)) ?? '')
    ) {
      toast({
        description:
          'Submission is not supported for this page. Please navigate to submission page.',
        variant: 'warning',
      });
      return;
    }

    if (!quote) {
      toast({
        description: 'No quote to submit.',
        variant: 'info',
      });
      return;
    }

    setIsSubmittingToPortal(true);

    handleTerminatingAction();
    try {
      const message: SubmitQuoteToPortalMessage = {
        targetTabId: tabId,
        action: portal.submitAction,
        data: {
          distance: maxDistance,
          flatRate: _.round(quote),
          isSubmitOnPortalEnabled:
            quickQuoteConfig?.isSubmitOnPortalEnabled || false,
        },
      };

      const sendMessage = () =>
        new Promise((resolve, reject) => {
          try {
            chrome.runtime.sendMessage(message, (response) => {
              if (chrome.runtime.lastError) {
                reject(chrome.runtime.lastError);
              } else if (response && response.success) {
                resolve(response);
              } else {
                reject(response);
              }
            });
          } catch (err) {
            reject(err);
          }
        });

      await sendMessage();
      const toastMsg = quickQuoteConfig?.isSubmitOnPortalEnabled
        ? 'Successfully submitted quote to portal'
        : 'Filled out quote form. Please review and submit';
      toast({
        description: toastMsg,
        variant: 'success',
      });
    } catch (error: any) {
      captureException(error, { functionName: 'handleSubmitToPortal' });

      if (error?.partialSuccess) {
        toast({
          description:
            'Entered quote in the portal, but could not submit. Please submit manually.',
          variant: 'warning',
        });
      } else {
        toast({
          description: 'Failed to submit quote to portal.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsSubmittingToPortal(false);
    }
  };

  return (
    <div className='mb-4'>
      <ExtendedFormProvider aiDefaultValues={true}>
        <FormProvider {...formMethods}>
          <TooltipProvider>
            <form
              onSubmit={handleSubmit(onSubmitForm)}
              className='grid gap-5 grid-cols-1 mx-0 w-full'
            >
              <div className='grid gap-3 grid-cols-1 w-full mx-0 mt-4'>
                <div className='text-md whitespace-nowrap text-grayscale-content-label font-semibold'>
                  Quote Information
                </div>

                {tmsIntegrations &&
                  tmsIntegrations.length > 0 &&
                  !isTaiOrGlobalTranzTMS && (
                    <div>
                      <RHFDebounceSelect
                        required={false}
                        name='customerName'
                        label='Customer'
                        control={control}
                        errors={errors}
                        data={customers}
                        isLoading={false}
                        showSearchParamDropdown={false}
                        refreshHandler={handleRefreshCustomers}
                        resetOptionsHandler={handleResetCustomerSearch}
                        fetchOptions={handleCustomerSearch}
                        mapOptions={mapCustomerToAntdOptions}
                      />
                    </div>
                  )}

                <div>
                  <Label name={'transportType'} required={true}>
                    Transport Type
                  </Label>
                  <Controller
                    name='transportType'
                    control={control}
                    rules={{ required: 'Required' }}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger className='w-full mt-1'>
                          <SelectValue placeholder='Choose' />
                        </SelectTrigger>
                        <SelectContent>
                          {transportTypeOptions.map(
                            (option: TransportTypeOption) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            )
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              <div className='flex justify-between items-start gap-2 w-full mx-0'>
                {/* Pickup */}
                <div className='w-full'>
                  <QuickQuoteTextInput
                    name='stops.0.location'
                    label='Pickup'
                    placeholder='ZIP or City, State'
                    inputClassName='placeholder:text-[12px]'
                    required
                  />
                </div>

                <ArrowRightIcon className='w-10 text-grayscale-icon-stroke mt-7' />

                {/* Dropoff */}
                <div className='w-full'>
                  <QuickQuoteTextInput
                    name='stops.1.location'
                    label='Dropoff'
                    placeholder='ZIP or City, State'
                    inputClassName='placeholder:text-[12px]'
                    required
                  />
                </div>
              </div>

              {hasAddedQuoteDates && (
                <div className='flex flex-col gap-3'>
                  <p className='text-md font-semibold'>Dates</p>
                  <div className='flex justify-between items-end gap-4 w-full mx-0'>
                    <div className='w-full'>
                      <Label name='pickupDate' className='text-base'>
                        Pickup
                      </Label>
                      <Controller
                        name={`pickupDate`}
                        control={control}
                        render={({ field }) => (
                          <div className='mt-1 flex flex-row gap-1'>
                            <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                              <div className='col-span-2'>
                                <DatePicker field={field} />
                              </div>
                            </div>
                          </div>
                        )}
                      />
                    </div>

                    <div className='w-full'>
                      <Label name='deliveryDate' className='text-base'>
                        Dropoff
                      </Label>
                      <Controller
                        name={`deliveryDate`}
                        control={control}
                        render={({ field }) => (
                          <div className='mt-1 flex flex-row gap-1'>
                            <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                              <div className='col-span-2'>
                                <DatePicker field={field} />
                              </div>
                            </div>
                          </div>
                        )}
                      />
                    </div>
                  </div>
                </div>
              )}

              {!hasAddedQuoteDates && (
                <AddDateDetailsButton
                  buttonNamePosthog={ButtonNamePosthog.AddQuoteDateDetails}
                  onClick={() => setHasAddedQuoteDates(true)}
                />
              )}

              {errors.root && (
                <div className='flex justify-center'>
                  <div className='w-auto text-sm border-red-500 bg-red-500 text-neutral-50 dark:border-red-900 dark:bg-red-900 dark:text-neutral-50 rounded-lg p-2 text-center'>
                    {errors.root.message}
                  </div>
                </div>
              )}

              {serviceUsesDAT &&
                hasGrantedDATPermissions !== null &&
                !hasGrantedDATPermissions && (
                  <div className='grid gap-3 w-full mx-0 bg-blue-bg border border-blue-main rounded-[4px] py-4 px-3'>
                    <div className='flex items-center justify-between'>
                      <DATLogo height={12} />
                      <Tooltip delayDuration={10}>
                        <TooltipTrigger>
                          <InfoCircleIcon className='w-5 h-5 text-blue-main cursor-pointer' />
                        </TooltipTrigger>
                        <TooltipContent>
                          Enable DAT using your email address
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    <div className='flex justify-between items-end gap-3 w-full'>
                      <div className='w-full flex flex-col grow-1'>
                        <label className='text-sm text-grayscale-content-label mb-1'>
                          Email Address
                        </label>
                        <Input
                          name='datEmailAddress'
                          placeholder='<EMAIL>'
                          className='!outline-none'
                          onChange={(e) => setDATEmailAddress(e.target.value)}
                        />
                      </div>

                      <Button
                        buttonNamePosthog={ButtonNamePosthog.EnableDATForUser}
                        className='w-20 h-8 text-sm mb-[1px] bg-orange-main/80'
                        onClick={handleEnableDATAccess}
                        disabled={isLoadingDAT || !datEmailAddress}
                        type='button'
                      >
                        {isLoadingDAT ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.EnableDATForUser
                        )}
                      </Button>
                    </div>
                  </div>
                )}

              <Button
                buttonNamePosthog={ButtonNamePosthog.GetQuickQuote}
                logProperties={getQuickQuoteProperties}
                type='submit'
                className='w-full'
                disabled={isSubmitting}
              >
                {isSubmitting ? <ButtonLoader /> : ButtonText.GetQuickQuote}
              </Button>

              {quote && !quoteNotConfident && (
                <div className='flex flex-col items-center py-2'>
                  <div className=' w-full text-left text-md text-grayscale-content-label font-semibold'>
                    Quote for
                  </div>
                  <h3 className='text-sm w-full text-left'>
                    {`
                      ${useHelperFunctions.toTitleCase(quote.stops[0].city)}, ${quote.stops[0].state} to
                      ${useHelperFunctions.toTitleCase(quote.stops[1].city)}, ${quote.stops[1].state}
                    `}
                  </h3>
                  {maxDistance > 0 && (
                    <h3 className='text-sm w-full text-left flex items-center gap-2'>
                      {`Distance: ${_.round(maxDistance)} miles`}
                      <Tooltip delayDuration={10}>
                        <TooltipTrigger asChild>
                          <InfoIcon className='h-4 w-4' />
                        </TooltipTrigger>
                        <TooltipContent>
                          {distanceSource && (
                            <p>{`Distance from ${integrationNameMap[distanceSource]}`}</p>
                          )}
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip delayDuration={10}>
                        <TooltipTrigger asChild>
                          <a
                            href={constructGoogleMapsUrl(quote.stops)}
                            target='_blank'
                            rel='noreferrer'
                            className='text-orange-main/60 hover:text-orange-main transition-colors'
                            aria-label='View route in Google Maps'
                          >
                            <MapIcon className='h-4 w-4' />
                          </a>
                        </TooltipTrigger>
                        <TooltipContent>
                          View route in Google Maps
                        </TooltipContent>
                      </Tooltip>
                    </h3>
                  )}

                  <div className='w-full h-0.5 bg-grayscale-border-divider mt-2' />

                  {/* Display quote cards from each integration (DAT, GS, etc) */}
                  <div className='flex flex-col gap-4 mt-4 w-full'>
                    {quoteCards.map((card) => (
                      <QuoteCard
                        key={card.type}
                        carrier={card}
                        isSelected={selectedCarrier === card.type}
                        onClick={() => {
                          setSelectedCarrier(card.type);
                          if (carrierCostType === CarrierCostType.Flat) {
                            setCarrierCost(_.round(card.cost));
                          } else {
                            // Use costPerMile if available
                            if (card?.costPerMile) {
                              setCarrierCost(card?.costPerMile);
                            } else {
                              // Otherwise, reset carrier cost to flat
                              setCarrierCostType(CarrierCostType.Flat);
                              setCarrierCost(_.round(card.cost));
                            }
                          }
                        }}
                        lowConfidenceThreshold={
                          quote.configuration?.lowConfidenceThreshold || 70
                        }
                        mediumConfidenceThreshold={
                          quote.configuration?.mediumConfidenceThreshold || 80
                        }
                      />
                    ))}
                  </div>
                </div>
              )}
              {/* Lane History Charts */}
              {(quoteNotConfident || quote) && (
                <>
                  {isQuoteLaneHistoryEnabled || isTMSLaneHistoryEnabled ? (
                    isLoadingLaneHistory ? (
                      <div className='flex flex-row justify-center gap-1'>
                        <p className='text-sm text-grayscale-content-input'>
                          Fetching Lane History...
                        </p>
                        <ButtonLoader />
                      </div>
                    ) : laneHistory &&
                      Object.values(laneHistory.resultsBySource).length ? (
                      // Generate a chart for each source
                      Object.entries(laneHistory.resultsBySource).map(
                        ([source, history], index) => {
                          let laneTierHistory = history[0];

                          const isTMSChart =
                            tmsLaneHistory &&
                            tmsLaneHistory.length > 0 &&
                            source === tmsLaneHistory[0].source;

                          // If there's TMS lane history and multiple tiers, graph the selected tier
                          if (
                            tmsLaneHistory &&
                            isTMSChart &&
                            tmsLaneHistorySelectedTierIndex !== null
                          ) {
                            laneTierHistory =
                              tmsLaneHistory[tmsLaneHistorySelectedTierIndex];
                          }

                          let laneTierOptions: Undef<LaneTier[]> = undefined;
                          if (tmsLaneHistory && isTMSChart) {
                            laneTierOptions = tmsLaneHistory
                              .filter((history) => Boolean(history.laneTier))
                              .map((history) => history.laneTier as LaneTier);
                          }

                          const yAxisMax = Math.max(
                            ...history[0].weeks
                              .filter((item) => Boolean(item.maxRate))
                              .map((item) => item.maxRate)
                          );

                          const tooltipElt = (
                            inputtedTransportType: TransportType,
                            proxiedTransportType: TransportType
                          ) => (
                            <div className='flex justify-between'>
                              <div className='flex items-baseline gap-x-2'>
                                <Tooltip delayDuration={10}>
                                  <TooltipTrigger className='border-b border-dashed border-black text-sm font-normal'>
                                    {`(${titleCase(proxiedTransportType)})`}
                                  </TooltipTrigger>
                                  <TooltipContent className='mr-1 font-normal max-w-60 whitespace-pre-wrap'>
                                    <p>{`Greenscreens does not support ${titleCase(inputtedTransportType)} quotes, showing equivalent ${titleCase(proxiedTransportType)} quote.`}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </div>
                            </div>
                          );

                          return (
                            <>
                              <GenericLineChart
                                data={laneTierHistory.weeks}
                                title={`${integrationNameMap[laneTierHistory.source]} Lane History`}
                                subtitle={
                                  laneTierHistory.inputtedTransportType !==
                                  laneTierHistory.proxiedTransportType
                                    ? tooltipElt(
                                        laneTierHistory.inputtedTransportType,
                                        laneTierHistory.proxiedTransportType
                                      )
                                    : titleCase(
                                        laneTierHistory.proxiedTransportType
                                      )
                                }
                                // Don't repeat description for each chart
                                description={
                                  index === 0
                                    ? laneTierHistory.isPercentile
                                      ? `${quote?.stops[0].state} to ${quote?.stops[1].state} rates from the last 4 weeks`
                                      : 'Rates from the last four weeks'
                                    : ''
                                }
                                toggleOptions={laneTierOptions}
                                selectedData={
                                  laneTierHistory.laneTier as string
                                }
                                toggleDataHandler={toggleLaneHistoryGraph}
                                chartConfig={
                                  laneTierHistory.isPercentile
                                    ? chartConfigPercentile
                                    : chartConfig
                                }
                                yAxisDomainMax={yAxisMax}
                                yAxisDomainMin={0}
                                yAxisWidth={yAxisMax > 999 ? 45 : 40}
                                thirdTooltipLabel='Loads'
                                dataKeys={
                                  [
                                    'maxRate',
                                    'averageRate',
                                    'lowestRate',
                                  ] as (keyof WeekLaneData)[]
                                }
                              />
                            </>
                          );
                        }
                      )
                    ) : (
                      <div className='flex justify-center'>
                        <p className='text-sm text-grayscale-content-input'>
                          No Lane History Available
                        </p>
                      </div>
                    )
                  ) : null}
                  <CarrierPriceCalculator
                    calculatorParent={CarrierPriceCalculatorParent.QuickQuote}
                    parentQuoteRequestId={parentQuoteRequestId}
                    showTitle={false}
                    mileage={maxDistance}
                    mileageSource={distanceSource}
                    finalPrice={finalPrice}
                    fuelEstimate={fuelEstimate}
                    datFuelSurcharge={datFuelSurcharge}
                    portalFuelSurchargeSource={
                      clickedSuggestion?.pipeline ===
                      SuggestionPipelines.QuickQuote
                        ? (clickedSuggestion?.source ?? null)
                        : null
                    }
                    portalFuelSurcharge={
                      clickedSuggestion?.pipeline ===
                      SuggestionPipelines.QuickQuote
                        ? clickedSuggestion?.suggested.fuelSurchargePerMile
                        : null
                    }
                    profit={profit}
                    profitType={profitType}
                    maxDistance={maxDistance}
                    terminatingActionHandler={handleTerminatingAction}
                    setProfitTypeHandler={setProfitType}
                    carrierCost={carrierCost}
                    carrierCostType={carrierCostType}
                    setCarrierCostTypeHandler={setCarrierCostType}
                    setCarrierCostHandler={setCarrierCost}
                    setProfitHandler={setProfit}
                    setFuelEstimateHandler={setFuelEstimate}
                    setFinalPriceHandler={setFinalPrice}
                    selectedQuickQuoteId={getSelectedQuickQuoteId()}
                  />
                </>
              )}
            </form>

            {quote && quote.configuration?.belowThresholdMessage && (
              <div className='w-auto text-sm border-red-500 bg-red-500 text-neutral-50 dark:border-red-900 dark:bg-red-900 dark:text-neutral-50 rounded-lg p-2 mb-4 text-center'>
                {quote.configuration?.belowThresholdMessage}
              </div>
            )}

            {(quote || quoteNotConfident) && (
              <div
                className='flex-col justify-center'
                ref={scrollResultsIntoViewRef}
              >
                <Divider className='border border-grayscale-border-divider my-6' />

                {/* Submit quote via third-party URL (e.g. shipper TMSes) */}
                {isQuoteSubmissionViaURLEnabled && hasThirdPartyQuoteURLs && (
                  <>
                    <h3>Submission Hyperlink Detected</h3>
                    <div className='w-full mt-4'>
                      <QuickQuoteTextInput
                        name='quoteNumber'
                        placeholder='Q000001'
                        label='Quote #'
                        required
                      />
                    </div>
                    <div className='w-full flex flex-row justify-between gap-2 mt-2'>
                      <div className='flex flex-col flex-1'>
                        <Label name='quoteExpirationDate' className='text-base'>
                          Expiration
                        </Label>
                        <Controller
                          name={`quoteExpirationDate`}
                          control={control}
                          render={({ field }) => (
                            <div className='mt-1 flex flex-row gap-1'>
                              <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                                <div className='col-span-2'>
                                  <DatePicker field={field} />
                                </div>
                              </div>
                            </div>
                          )}
                        />
                        {errors.quoteExpirationDate && (
                          <p className='text-red-500 text-xs'>
                            {errors.quoteExpirationDate.message}
                          </p>
                        )}
                      </div>
                      <div className='flex flex-col flex-1'>
                        <Label name='Eta' className='text-base'>
                          ETA
                        </Label>
                        <Controller
                          name={`quoteEta`}
                          control={control}
                          render={({ field }) => (
                            <div className='mt-1 flex flex-row gap-1'>
                              <div className='grid grid-cols-2 gap-1 flex-1 mx-0 w-full'>
                                <div className='col-span-2'>
                                  <DatePicker field={field} />
                                </div>
                              </div>
                            </div>
                          )}
                        />
                        {errors.quoteEta && (
                          <p className='text-red-500 text-xs'>
                            {errors.quoteEta.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <Button
                      className='w-full h-8 text-sm mt-4'
                      type='button'
                      buttonNamePosthog={ButtonNamePosthog.SubmitQuoteViaURL}
                      disabled={loadingDraftReply}
                      onClick={() => {
                        const currentSelectedQuickQuoteIdForURL =
                          getSelectedQuickQuoteId();
                        useHelperFunctions.handleQuoteSubmissionViaURL({
                          email,
                          quote,
                          getValues,
                          setError,
                          setLoadingDraftReply,
                          finalPrice,
                          isTMSQuoteSubmissionEnabled,
                          isSubmitToTMS,
                          setCreatedQuoteId,
                          clickedSuggestion,
                          setCurrentState,
                          parentQuoteRequestId,
                          profit,
                          profitType,
                          carrierCost,
                          carrierCostType,
                          maxDistance,
                          selectedQuickQuoteId:
                            currentSelectedQuickQuoteIdForURL,
                        });
                      }}
                    >
                      {loadingDraftReply ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.SubmitQuoteViaURL
                      )}
                    </Button>
                  </>
                )}

                {/* Submit to Portal buttons if on an integrated portal */}
                {showSubmitToPortalButtons && (
                  <div className='mt-4 flex gap-x-2 w-full items-center'>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className='flex-1'>
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.SubmitLineHaulToPortal
                              }
                              logProperties={{
                                portal: (
                                  clickedSuggestion as SuggestedQuoteChange
                                )?.source,
                                selectedQuote: selectedCarrier,
                                quoteRequestId: parentQuoteRequestId,
                              }}
                              className='w-full bg-white text-orange-main border border-orange-border-main hover:bg-orange-bg'
                              type='button'
                              disabled={isSubmittingToPortal || !finalFlatPrice}
                              onClick={() =>
                                handleSubmitToPortal(
                                  finalFlatPrice
                                    ? finalFlatPrice - fuelEstimate
                                    : null
                                )
                              }
                            >
                              {isSubmittingToPortal ? (
                                <ButtonLoader />
                              ) : (
                                `${quickQuoteConfig?.isSubmitOnPortalEnabled ? 'Submit' : 'Input'} line haul`
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        {!finalFlatPrice && (
                          <TooltipContent>
                            <p>Unable to submit on this page</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className='flex-1'>
                            <Button
                              buttonNamePosthog={
                                ButtonNamePosthog.SubmitTotalToPortal
                              }
                              logProperties={{
                                portal: (
                                  clickedSuggestion as SuggestedQuoteChange
                                )?.source,
                                selectedQuote: selectedCarrier,
                                quoteRequestId: parentQuoteRequestId,
                              }}
                              className='w-full'
                              type='button'
                              disabled={isSubmittingToPortal || !finalFlatPrice}
                              onClick={() =>
                                handleSubmitToPortal(finalFlatPrice)
                              }
                              variant='default'
                            >
                              {isSubmittingToPortal ? (
                                <ButtonLoader />
                              ) : (
                                `${quickQuoteConfig?.isSubmitOnPortalEnabled ? 'Submit' : 'Input'} total sell`
                              )}
                            </Button>
                          </div>
                        </TooltipTrigger>
                        {!finalFlatPrice && (
                          <TooltipContent>
                            <p>Unable to submit on this page</p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                )}

                {/* Show draft response if Drumkit running on email platform (Gmail, Outlook, Front) */}
                {isEmailPlatform(drumkitPlatform) && (
                  <div className='mt-4 '>
                    <h3 className='mb-1'>Draft Response</h3>

                    <div className='relative'>
                      <Button
                        buttonNamePosthog={
                          ButtonNamePosthog.CopyQuoteToClipboard
                        }
                        logProperties={copyToClipboardProperties}
                        className={cn(
                          'absolute h-6 w-6 p-0 -top-6 right-0 border-none',
                          hasCopiedDraftResponse
                            ? 'cursor-default'
                            : 'cursor-pointer'
                        )}
                        variant='ghost'
                        type='button'
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          !hasCopiedDraftResponse && handleCopyToClipboard();
                        }}
                      >
                        {hasCopiedDraftResponse ? (
                          <Tooltip open={true}>
                            <TooltipTrigger asChild>
                              <CheckIcon className='h-4 w-4' />
                            </TooltipTrigger>
                            <TooltipContent>Copied!</TooltipContent>
                          </Tooltip>
                        ) : (
                          <CopyIcon className='h-4 w-4' />
                        )}
                      </Button>

                      {/* Draft Response Textarea */}
                      <Textarea
                        name='emailBody'
                        className='p-2 h-36 rounded-[4px]'
                        value={draftResponse}
                        onChange={(e) => {
                          setDraftResponse(e.target.value);
                          !userEditedDraft && setUserEditedDraft(true);
                        }}
                      />
                    </div>

                    {/* If distance exists, allow user to toggle format of final price in response */}
                    {Boolean(maxDistance) && (
                      <div className='flex flex-row items-center whitespace-nowrap min-w-0 mt-1 overflow-hidden'>
                        <span className='text-[13px] xxs:text-xs text-grayscale-content-description mr-2'>
                          Show:
                        </span>
                        <div className='inline-flex rounded-[4px] max-h-5 border border-grayscale-border-input text-xs'>
                          <button
                            type='button'
                            title={'Flat Rate'}
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.Flat)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === CarrierCostType.Flat
                                ? 'text-[#FE9659] font-medium bg-orange-bg'
                                : 'text-grayscale-content-description hover:text-[#FE9659]'
                            }`}
                          >
                            Flat Rate
                          </button>
                          <button
                            title={'Per Mile Rate'}
                            type='button'
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.PerMile)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === CarrierCostType.PerMile
                                ? 'text-[#FE9659] font-medium bg-orange-bg'
                                : 'text-grayscale-content-description hover:text-[#FE9659]'
                            }`}
                          >
                            Per Mile
                          </button>
                          <button
                            title={'Both Rates'}
                            type='button'
                            onClick={() => setFinalPriceFormat('Both')}
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === 'Both'
                                ? 'text-[#FE9659] font-medium bg-orange-bg'
                                : 'text-grayscale-content-description hover:text-[#FE9659]'
                            }`}
                          >
                            Both
                          </button>
                          <button
                            title={'Customer Linehaul'}
                            type='button'
                            onClick={() =>
                              setFinalPriceFormat(CarrierCostType.Linehaul)
                            }
                            className={`px-1.5 transition-colors ${
                              finalPriceFormat === 'Linehaul'
                                ? 'text-[#FE9659] font-medium bg-orange-bg'
                                : 'text-grayscale-content-description hover:text-[#FE9659]'
                            }`}
                          >
                            Linehaul
                          </button>
                        </div>
                      </div>
                    )}

                    {/** Reply drafts are only supported on Outlook for now */}
                    {drumkitPlatform === DrumkitPlatform.Outlook && (
                      <Button
                        className='w-full h-8 text-sm mt-4'
                        type='button'
                        buttonNamePosthog={
                          isOutlookReply
                            ? ButtonNamePosthog.AddReplyToCurrentDraft
                            : ButtonNamePosthog.CreateDraftReply
                        }
                        disabled={loadingDraftReply}
                        onClick={() => handleDraftResponse()}
                      >
                        {isOutlookReply ? (
                          ButtonText.AddReplyToCurrentDraft
                        ) : loadingDraftReply ? (
                          <ButtonLoader />
                        ) : (
                          ButtonText.CreateDraftReply
                        )}
                      </Button>
                    )}
                  </div>
                )}

                {isTMSQuoteSubmissionEnabled && (
                  <>
                    <Divider className='border border-grayscale-border-divider my-6' />

                    <h3>Save quote in TMS</h3>

                    <div className='mt-4'>
                      <RHFDebounceSelect
                        required={true}
                        name='customerName'
                        label='Customer'
                        control={control}
                        errors={errors}
                        data={customers}
                        isLoading={false}
                        showSearchParamDropdown={false}
                        refreshHandler={handleRefreshCustomers}
                        resetOptionsHandler={handleResetCustomerSearch}
                        fetchOptions={handleCustomerSearch}
                        mapOptions={mapCustomerToAntdOptions}
                      />
                      <a
                        className='underline text-[12px] text-orange-main'
                        target='_blank'
                        rel='noreferrer'
                        href={`https://app.turvo.com/#/${tmsTenant}/accounts/cards?type=shipper`}
                      >
                        or create a new customer.
                      </a>
                    </div>

                    {createdQuoteId ? (
                      <>
                        <div className='whitespace-pre-wrap my-3 rounded py-3 text-grayscale-content-label px-4 bg-green-bg'>
                          <p className='mb-2'>Quote Created 🎉</p>
                          <p className='mb-2 text-[14px]'>
                            <b className='text-[14px]'>Quote ID #: </b>
                            {createdQuoteId}
                          </p>
                          <p className='mb-1 text-[14px]'>
                            {/** Feature is only supported on Turvo for now */}
                            <a
                              className='underline'
                              target='_blank'
                              rel='noreferrer'
                              href={`https://app.turvo.com/#/${tmsTenant}/shipments/${createdQuoteId}/details`}
                            >
                              Access the created quote for more details
                            </a>
                          </p>
                        </div>
                      </>
                    ) : null}

                    <Button
                      className='w-full h-8 text-sm mt-4'
                      type='button'
                      buttonNamePosthog={ButtonNamePosthog.SubmitQuoteToTMS}
                      disabled={submitQuoteLoading}
                      onClick={async () => await handleSubmitQuoteToTMS()}
                    >
                      {submitQuoteLoading ? (
                        <ButtonLoader />
                      ) : (
                        ButtonText.SubmitQuoteToTMS
                      )}
                    </Button>
                  </>
                )}
              </div>
            )}
          </TooltipProvider>
        </FormProvider>
      </ExtendedFormProvider>
    </div>
  );
}
