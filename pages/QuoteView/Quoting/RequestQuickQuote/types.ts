import {
  FieldPath,
  UseFormGetValues,
  UseFormReturn,
  UseFormSetError,
  UseFormSetValue,
} from 'react-hook-form';

import { QuoteCardType } from 'components/QuoteCard';
import { RHFTextInput } from 'components/input/RHFTextInput';
import { IntegrationCore, QuickQuoteConfig } from 'contexts/serviceContext';
import { SidebarState } from 'contexts/sidebarStateContext';
import { LaneHistoryResponse } from 'lib/api/getLaneHistory';
import {
  QuickQuoteInputs,
  QuickQuoteResponse,
  getQuickQuote,
} from 'lib/api/getQuickQuote';
import { SubmitQuoteToTMSResponse } from 'lib/api/submitQuoteToTMS';
import { Email } from 'types/Email';
import { TMSCustomer } from 'types/Load';
import { TransportType } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import { GenericSuggestion } from 'types/suggestions/CoreSuggestions';
import { GenericCompanySearchableFields } from 'utils/loadInfoAndBuilding';

import { QuoteCountries } from './helperFunctions';

export interface TransportTypeOption {
  value: TransportType;
  label: string;
}

export const getTransportTypeOptions = (
  quickQuoteConfig?: Maybe<QuickQuoteConfig>
): TransportTypeOption[] => {
  const options: TransportTypeOption[] = [
    {
      value: TransportType.VAN,
      label: 'Van',
    },
    {
      value: TransportType.REEFER,
      label: 'Reefer',
    },
    {
      value: TransportType.FLATBED,
      label: 'Flatbed',
    },
    {
      value: TransportType.HOTSHOT,
      label: 'Hotshot (Flatbed)',
    },
    {
      value: TransportType.BOXTRUCK,
      label: 'Box Truck (Van)',
    },
  ];
  if (
    quickQuoteConfig?.otherTransportTypes?.some(
      (t) => t.toUpperCase() === TransportType.SPRINTER
    )
  ) {
    options.push({
      value: TransportType.SPRINTER,
      label: 'Sprinter (Van)',
    });
  }
  return options;
};

export enum QuoteTypeInSource {
  GS_Network = 'gs_network',
  GS_BuyPower = 'gs_buypower',
  DAT = 'dat',
  GlobalTranz = 'globaltranz',
  TruckStop_Booked = 'truckstop_booked',
  TruckStop_Posted = 'truckstop_posted',
}

export enum ProfitType {
  // EX: If CarrierCostType = PerMile & ProfitType = Amount, then profit is in $ per mile
  // EX: If CarrierCostType = Flat & ProfitType = Amount, then profit is total $ value
  Amount = 'Amount',
  Percentage = 'Percentage',
}

export enum CarrierCostType {
  Flat = 'Flat',
  PerMile = 'PerMile',
  Linehaul = 'Linehaul',
}

export enum DATQuoteTimeframe {
  '3_DAYS' = '3 day',
  '7_DAYS' = '7 day',
  '15_DAYS' = '15 day',
  '30_DAYS' = '30 day',
  '60_DAYS' = '60 day',
  '90_DAYS' = '90 day',
  '180_DAYS' = '180 day',
  '365_DAYS' = '365 day',
  '1_MONTH' = '1 month',
}

export enum DATQuoteLocationType {
  '3_DIGIT_ZIP' = '3_DIGIT_ZIP',
  'MARKET_AREA' = 'MARKET_AREA',
  'EXTENDED_MARKET_AREA' = 'EXTENDED_MARKET_AREA',
  'STATE' = 'STATE',
  'REGION' = 'REGION',
  'COUNTRY' = 'COUNTRY',
}

// Generic Drumkit enums
export enum LaneTier {
  _3DigitZipLaneTier = '3-Digit-Zip',
  CityStateLaneTier = 'City-to-City',
  MarketLaneTier = 'Market-to-Market', // Not supported by Mcleod rn
  StateLaneTier = 'State-to-State',
}

export const laneTierMap: Record<LaneTier, string> = {
  [LaneTier._3DigitZipLaneTier]: '3-digit zip',
  [LaneTier.CityStateLaneTier]: 'City',
  [LaneTier.MarketLaneTier]: 'Market',
  [LaneTier.StateLaneTier]: 'State',
};

export type QuickQuoteTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<QuickQuoteInputs> };

export type SendGreenscreensQuoteProps = {
  quote: QuickQuoteResponse;
  setGreenscreensQuoteID: React.Dispatch<React.SetStateAction<string>>;
};

export type SendUserQuoteProps = {
  email: Maybe<Email>;
  quote: Maybe<QuickQuoteResponse>;
  parentQuoteRequestId: number;
  greenscreensQuoteID: string;
  carrierCost: number;
  profit: number;
  profitType: ProfitType;
  finalPrice: number;
  draftResponse: string;
};

export type FetchQuoteNumberProps = {
  email: Maybe<Email>;
  setHasThirdPartyQuoteURLs: React.Dispatch<React.SetStateAction<boolean>>;
  setValue: UseFormSetValue<QuickQuoteInputs>;
};

export type DATQuoteMetadata = {
  timeframe: keyof typeof DATQuoteTimeframe;
  companies: number;
  reports: number;
  originName: string;
  originType: keyof typeof DATQuoteLocationType;
  destinationName: string;
  destinationType: keyof typeof DATQuoteLocationType;
};

export type FetchLaneRateFromServiceProps = {
  emailId: number;
  threadId: string;
  quoteRequestId: number;
  setQuoteCards: React.Dispatch<React.SetStateAction<QuoteCardType[]>>;
  updatedFormValues: QuickQuoteInputs;
};

export type FetchCustomersProps = {
  setInitialCustomers: React.Dispatch<
    React.SetStateAction<Maybe<TMSCustomer[]>>
  >;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  setTMSTenant: React.Dispatch<React.SetStateAction<MaybeUndef<string>>>;
  tmsIntegrations: IntegrationCore[];
};

export type HandleRefreshCustomersProps = {
  setInitialCustomers: React.Dispatch<
    React.SetStateAction<Maybe<TMSCustomer[]>>
  >;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  tmsIntegrations: IntegrationCore[];
};

export type HandleResetCustomerSearchProps = {
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
  initialCustomers: Maybe<TMSCustomer[]>;
};

export type HandleCustomerSearchProps = {
  field: GenericCompanySearchableFields;
  value: string;
  tmsIntegrations: IntegrationCore[];
  customers: Maybe<TMSCustomer[]>;
  setCustomers: React.Dispatch<React.SetStateAction<Maybe<TMSCustomer[]>>>;
};

export type OnSubmitFormProps = {
  formValues: QuickQuoteInputs;
  getValues: UseFormGetValues<QuickQuoteInputs>;
  setIsSubmitToTMS: React.Dispatch<React.SetStateAction<boolean>>;
  setCreatedQuoteId: React.Dispatch<React.SetStateAction<MaybeUndef<number>>>;
  setQuote: React.Dispatch<React.SetStateAction<Maybe<QuickQuoteResponse>>>;
  setQuoteCards: React.Dispatch<React.SetStateAction<QuoteCardType[]>>;
  isQuoteSubmissionViaURLEnabled: boolean;
  email: Maybe<Email>;
  setHasThirdPartyQuoteURLs: React.Dispatch<React.SetStateAction<boolean>>;
  setValue: UseFormSetValue<QuickQuoteInputs>;
  isGetLaneRateFromServiceEnabled: boolean;
  clickedSuggestion: Maybe<GenericSuggestion>;
  formMethods: UseFormReturn<QuickQuoteInputs>;
  setQuoteNotConfident: React.Dispatch<React.SetStateAction<boolean>>;
  getQuickQuote: typeof getQuickQuote;
  isQuoteLaneHistoryEnabled: boolean;
  isTMSLaneHistoryEnabled: boolean;
  setIsLoadingLaneHistory: React.Dispatch<React.SetStateAction<boolean>>;
  setLaneHistory: React.Dispatch<
    React.SetStateAction<Maybe<LaneHistoryResponse>>
  >;
  setCarrierCost: React.Dispatch<React.SetStateAction<number>>;
  setProfit: React.Dispatch<React.SetStateAction<number>>;
  profitType: ProfitType;
  setError: UseFormSetError<QuickQuoteInputs>;
  setParentQuoteRequestId: React.Dispatch<React.SetStateAction<number>>;
  setGreenscreensQuoteID: React.Dispatch<React.SetStateAction<string>>;
  isQuoteSubmissionToServiceEnabled: boolean;
  setDATFuelSurcharge: React.Dispatch<React.SetStateAction<Maybe<number>>>;
  selectedQuickQuoteId?: number;
};

export type EnableDATIndividualAccessProps = {
  datEmailAddress: string;
  setHasGrantedDATPermissions: React.Dispatch<
    React.SetStateAction<Maybe<boolean>>
  >;
};

export type ProcessQuoteTMSSubmissionProps = {
  customerId: string;
  finalPrice: Maybe<number>;
  getValues: UseFormGetValues<QuickQuoteInputs>;
};

export type HandleQuoteSubmissionViaURLProps = {
  email: Maybe<Email>;
  quote: Maybe<QuickQuoteResponse>;
  getValues: UseFormGetValues<QuickQuoteInputs>;
  setError: UseFormSetError<QuickQuoteInputs>;
  setLoadingDraftReply: React.Dispatch<React.SetStateAction<boolean>>;
  finalPrice: Maybe<number>;
  isTMSQuoteSubmissionEnabled: boolean;
  isSubmitToTMS: boolean;
  setCreatedQuoteId: React.Dispatch<React.SetStateAction<MaybeUndef<number>>>;
  clickedSuggestion: Maybe<GenericSuggestion>;
  setCurrentState: React.Dispatch<React.SetStateAction<SidebarState>>;
  parentQuoteRequestId: number;
  profit: number;
  profitType: ProfitType;
  carrierCost: number;
  carrierCostType: CarrierCostType;
  maxDistance: number;
  selectedQuickQuoteId?: number;
};

export interface HelperFunctions {
  toTitleCase: (str: string) => string;
  parseLocation: (location: string) => Maybe<{
    zip: string;
    city: string;
    state: string;
    country: QuoteCountries;
  }>;
  sendGreenscreensQuote: (props: SendGreenscreensQuoteProps) => Promise<void>;
  sendUserQuote: (props: SendUserQuoteProps) => Promise<void>;
  fetchQuoteNumber: (props: FetchQuoteNumberProps) => Promise<void>;
  fetchLaneRateFromService: (
    props: FetchLaneRateFromServiceProps
  ) => Promise<void>;
  fetchCustomers: (props: FetchCustomersProps) => Promise<void>;
  onSubmitForm: (props: OnSubmitFormProps) => Promise<void>;
  enableDATIndividualAccess: (
    props: EnableDATIndividualAccessProps
  ) => Promise<void>;
  handleQuoteLaneHistory: (
    laneHistoryResponse: LaneHistoryResponse
  ) => QuoteCardType[];
  processQuoteTMSSubmission: (
    props: ProcessQuoteTMSSubmissionProps
  ) => Promise<MaybeUndef<SubmitQuoteToTMSResponse>>;
  handleQuoteSubmissionViaURL: (
    props: HandleQuoteSubmissionViaURLProps
  ) => Promise<void>;
}
