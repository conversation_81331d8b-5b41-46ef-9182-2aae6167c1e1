import { useEffect, useMemo, useRef, useState } from 'react';

import { ChevronDown, ChevronUp, Mail } from 'lucide-react';

import { Label } from 'components/Label';
import { TooltipProvider } from 'components/Tooltip';
import { TMSLocationWithDistance } from 'types/Load';

import CarrierSelectDropdown from './CarrierSelectDropdown';
import SelectedCarrierTooltip from './SelectedCarrierTooltip';
import ShowMoreLessButton from './ShowMoreCarriers';
import { CarrierFormConfig, SelectedCarrierData } from './types';

interface CarrierSelectSectionProps {
  // TODO: refactor so that carrier groups don't need to be mapped to TMSLocationWithDistance
  carrierLocations: TMSLocationWithDistance[]; // list of carriers
  selectedCarriers: Record<string, boolean>;
  toggleSelection: (carrierId: string) => void;
  config: CarrierFormConfig;
}

const MAX_SELECTED_CARRIERS_DISPLAYED = 6;

export default function CarrierSelectSection({
  carrierLocations,
  selectedCarriers,
  toggleSelection,
  config,
}: CarrierSelectSectionProps) {
  const [showAllSelectedCarriers, setShowAllSelectedCarriers] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<TMSLocationWithDistance[]>(
    []
  );
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedCarriersData = useMemo(() => {
    const carriersData: SelectedCarrierData[] = [];
    Object.entries(selectedCarriers).forEach(([carrierId, isSelected]) => {
      if (isSelected) {
        const carrier = carrierLocations.find(
          (loc) => loc.externalTMSID === carrierId
        );
        if (carrier) {
          const carrierEmails: string[] = [];
          if (carrier.emails && carrier.emails.length > 0) {
            carrierEmails.push(
              ...carrier.emails.filter((e): e is string => !!e)
            );
          } else if (carrier.email) {
            carrierEmails.push(carrier.email);
          }

          if (carrierEmails.length > 0) {
            carriersData.push({
              id: carrierId,
              name:
                carrier.carrier?.name || carrier.name || `Carrier ${carrierId}`,
              city: carrier.city || null,
              state: carrier.state || null,
              emails: Array.from(new Set(carrierEmails)),
              notes: carrier.notes || null,
              milesDistance: carrier.milesDistance || null,
            });
          }
        }
      }
    });
    return carriersData;
  }, [selectedCarriers, carrierLocations]);

  const displayedSelectedCarriers = useMemo(() => {
    if (showAllSelectedCarriers) {
      return selectedCarriersData;
    }
    return selectedCarriersData.slice(0, MAX_SELECTED_CARRIERS_DISPLAYED);
  }, [selectedCarriersData, showAllSelectedCarriers]);

  const numberOfSelectedCarriers = useMemo(() => {
    return selectedCarriersData.length;
  }, [selectedCarriersData]);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const currentSearchTerm = searchTerm.trim().toLowerCase();
    let newSearchResultsList: TMSLocationWithDistance[];

    if (currentSearchTerm === '') {
      newSearchResultsList = carrierLocations.filter(
        (carrier) =>
          !(carrier.externalTMSID && selectedCarriers[carrier.externalTMSID])
      );
    } else {
      newSearchResultsList = carrierLocations.filter((carrier) => {
        const isAlreadySelected =
          carrier.externalTMSID && selectedCarriers[carrier.externalTMSID];
        if (isAlreadySelected) return false;

        const nameMatch =
          carrier.carrier?.name?.toLowerCase().includes(currentSearchTerm) ||
          carrier.name?.toLowerCase().includes(currentSearchTerm);
        const emailMatch =
          carrier.emails?.some((e) =>
            e.toLowerCase().includes(currentSearchTerm)
          ) || carrier.email?.toLowerCase().includes(currentSearchTerm);

        return nameMatch || emailMatch;
      });
    }
    setSearchResults(newSearchResultsList);
  }, [searchTerm, selectedCarriers, carrierLocations]);

  const handleSearchTermChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const term = event.target.value;
    setSearchTerm(term);
    setIsDropdownOpen(true);
  };

  const handleSearchResultClick = (carrierId: string) => {
    toggleSelection(carrierId);
    setSearchTerm('');
    setIsDropdownOpen(true);
  };

  const handleDropdownToggle = () => {
    setIsDropdownOpen((prev) => !prev);
  };

  const handleInputFocus = () => {
    setIsDropdownOpen(true);
  };

  const handleToggleShowAll = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setShowAllSelectedCarriers(!showAllSelectedCarriers);
  };

  return (
    <div className='flex flex-col gap-1'>
      <Label name='to'>
        {numberOfSelectedCarriers === 0 ? (
          <EmptyDropdownLabel
            config={config}
            carrierLocationsLength={carrierLocations.length}
          />
        ) : (
          <SelectedCarriersDropdownLabel
            numberOfSelectedCarriers={numberOfSelectedCarriers}
          />
        )}
      </Label>
      <TooltipProvider>
        <div
          ref={dropdownRef}
          className='relative flex flex-wrap gap-1.5 border border-grayscale-border-input rounded-[4px] p-1.5 text-left text-xs items-center'
        >
          {/* Selected carriers */}
          {displayedSelectedCarriers.map((carrier) => (
            <SelectedCarrierTooltip
              key={carrier.id}
              carrier={carrier}
              onRemove={toggleSelection}
            />
          ))}

          {/* Carrier search input */}
          <div className='flex items-center gap-1 w-full justify-between'>
            <input
              type='text'
              value={searchTerm}
              onChange={handleSearchTermChange}
              onFocus={handleInputFocus}
              placeholder={
                displayedSelectedCarriers.length > 0
                  ? 'Add additional carriers to email'
                  : `Search carriers by name or email`
              }
              aria-label='Search for carriers to add to email list'
              className='flex-grow p-1 outline-none bg-transparent hover:bg-gray-50 rounded-[4px] text-xs placeholder-grayscale-content-tertiary min-w-[150px]'
            />
            {/* Carrier search dropdown toggle button */}
            <button
              type='button'
              aria-label={
                isDropdownOpen ? 'Close search results' : 'Open search results'
              }
              onClick={handleDropdownToggle}
              className={`p-0.5 text-grayscale-content-label ${
                isDropdownOpen
                  ? 'text-primary border border-primary'
                  : 'hover:bg-gray-100 border border-transparent'
              } focus:outline-none rounded-full`}
            >
              {isDropdownOpen ? (
                <ChevronUp size={16} />
              ) : (
                <ChevronDown size={16} />
              )}
            </button>
          </div>

          {/* Carrier search dropdown */}
          {isDropdownOpen && searchResults.length > 0 && (
            <CarrierSelectDropdown
              searchResults={searchResults}
              onCarrierSelect={handleSearchResultClick}
              isOpen={isDropdownOpen}
            />
          )}

          {selectedCarriersData.length > MAX_SELECTED_CARRIERS_DISPLAYED &&
            !searchTerm &&
            !isDropdownOpen && (
              <ShowMoreLessButton
                isShowingAll={showAllSelectedCarriers}
                totalCount={selectedCarriersData.length}
                displayedCount={MAX_SELECTED_CARRIERS_DISPLAYED}
                onToggle={handleToggleShowAll}
              />
            )}
        </div>
      </TooltipProvider>
    </div>
  );
}

interface EmptyDropdownLabelProps {
  config: CarrierFormConfig;
  carrierLocationsLength: number;
}

const EmptyDropdownLabel: React.FC<EmptyDropdownLabelProps> = ({
  config,
  carrierLocationsLength,
}) => (
  <div className='flex items-center gap-1'>
    <span className='text-grayscale-content-label text-wrap'>
      {config.isFindCarrierByLocationEnabled
        ? `Contact up to ${carrierLocationsLength} carriers near pickup`
        : 'Select carriers to email'}
    </span>
    <span className='text-red-500'>*</span>
  </div>
);

interface SelectedCarriersDropdownLabelProps {
  numberOfSelectedCarriers: number;
}

const SelectedCarriersDropdownLabel: React.FC<
  SelectedCarriersDropdownLabelProps
> = ({ numberOfSelectedCarriers }) => (
  <div className='flex items-center gap-1'>
    Sending {numberOfSelectedCarriers === 1 ? 'email' : 'separate emails'} to{' '}
    <div className='flex items-center gap-1 text-primary'>
      {numberOfSelectedCarriers} <Mail size={14} />
    </div>
    {numberOfSelectedCarriers === 1 ? 'carrier' : 'carriers'}:
  </div>
);
