import { Controller, useFormContext } from 'react-hook-form';

import { ErrorMessage } from '@hookform/error-message';

import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Textarea } from 'components/Textarea';
import { Input } from 'components/input/Input';
import ButtonLoader from 'components/loading/ButtonLoader';
import AttachmentChecklist from 'pages/QuoteView/Quoting/CarrierQuote/AttachmentChecklist';
import {
  CarrierFormConfig,
  CarrierQuoteInputs,
} from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { Email } from 'types/Email';
import { TMSLocationWithDistance } from 'types/Load';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';

import AttachmentUpload, { ProcessedAttachment } from './AttachmentUpload';
import CarrierSelectSection from './CarrierSelectSection';
import SelectAllCarriersCheckbox from './SelectAllCarriersCheckbox';

// import EligibleCarrierList from './EligibleCarrierList';

interface MessageCarrierFormProps {
  email: Maybe<Email>;
  loading: boolean;
  carrierLocations: TMSLocationWithDistance[];
  selectedCarriers: Record<string, boolean>;
  toggleSelection: (carrierId: string) => void;
  setNewAttachments: (files: ProcessedAttachment[]) => void;
  config: CarrierFormConfig;
  areAllCarriersSelected: boolean;
  onToggleSelectAllCarriers: () => void;
}

export default function MessageCarrierForm({
  email,
  loading,
  carrierLocations,
  selectedCarriers,
  toggleSelection,
  setNewAttachments,
  config,
  areAllCarriersSelected,
  onToggleSelectAllCarriers,
}: MessageCarrierFormProps) {
  const {
    control,
    formState: { errors },
  } = useFormContext<CarrierQuoteInputs>();

  return (
    <div className='grid gap-3 grid-cols-1 w-full mx-0 mt-2'>
      <div className='flex flex-col gap-1 mt-1'>
        <h2 className='text-md whitespace-nowrap text-grayscale-content-label font-semibold'>
          Email Carriers
        </h2>
        <p className='text-xs text-grayscale-content-label italic mb-1'>
          Sending as separate emails to each carrier.
        </p>

        <div className='flex flex-col gap-3'>
          <div className='flex flex-col gap-1'>
            <Label name='from'>From:</Label>
            <Controller
              name='from'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 5,
                  message: 'Minimum length is 5 characters',
                },
              }}
              render={({ field: { ref, onChange, value } }) => (
                <Input
                  type='text'
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  placeholder='<EMAIL>'
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name='from'
              render={({ message }: { message: string }) => (
                <p className='text-red-500 text-xs'>{message}</p>
              )}
            />
          </div>

          {/* Sending to */}
          <div className='flex flex-col gap-1'>
            {carrierLocations.length > 0 && (
              <SelectAllCarriersCheckbox
                areAllCarriersSelected={areAllCarriersSelected}
                onToggleSelectAllCarriers={onToggleSelectAllCarriers}
                disabled={carrierLocations.length === 0}
              />
            )}
            <CarrierSelectSection
              carrierLocations={carrierLocations}
              selectedCarriers={selectedCarriers}
              toggleSelection={toggleSelection}
              config={config}
            />
          </div>

          <div className='flex flex-col gap-1'>
            <Label name='subject'>Subject</Label>
            <Controller
              name='subject'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 5,
                  message: 'Minimum length is 5 characters',
                },
              }}
              render={({ field: { ref, onChange, value } }) => (
                <Textarea
                  className='min-h-[20px]'
                  value={value}
                  onChange={onChange}
                  ref={ref}
                  rows={Math.max(2, value.split('\n').length || 1)}
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name='subject'
              render={({ message }: { message: string }) => (
                <p className='text-red-500 text-xs'>{message}</p>
              )}
            />
          </div>

          <div className='flex flex-col gap-1'>
            <Label name='cc'>Cc:</Label>
            <Controller
              name='cc'
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type='text'
                  placeholder='Comma-separated emails (optional)'
                />
              )}
            />
          </div>

          <div className='flex flex-col gap-1'>
            <Label name='emailBody'>Body</Label>
            <Controller
              name='emailBody'
              control={control}
              rules={{
                required: { value: true, message: 'Required' },
                minLength: {
                  value: 20,
                  message: 'Minimum length is 20 characters',
                },
              }}
              render={({ field: { ref, onChange, value } }) => (
                <Textarea
                  name='emailBody'
                  className='p-2 min-h-80'
                  value={value}
                  onChange={onChange}
                  ref={ref}
                />
              )}
            />
            <ErrorMessage
              errors={errors}
              name='emailBody'
              render={({ message }: { message: string }) => (
                <p className='text-red-500 text-xs'>{message}</p>
              )}
            />
          </div>
        </div>
      </div>

      <div className='flex flex-col gap-1 my-3'>
        <h3 className='text-md whitespace-nowrap text-grayscale-content-label font-semibold'>
          Attachments
        </h3>
        <AttachmentChecklist email={email} />

        <div className='border-t border-grayscale-border-primary pt-2 flex flex-col gap-2'>
          <p className='text-xs text-grayscale-content-label font-medium'>
            Upload new files
          </p>
          <AttachmentUpload onFilesChange={setNewAttachments} />
        </div>
      </div>

      <Button
        buttonNamePosthog={ButtonNamePosthog.EmailCarriers}
        type='submit'
        className='w-full'
        disabled={loading}
      >
        {loading ? <ButtonLoader /> : ButtonText.EmailCarriers}
      </Button>
    </div>
  );
}
