import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore react-dropzone is in the parent dir
import { FileRejection, FileWithPath, useDropzone } from 'react-dropzone';

import { XIcon } from 'lucide-react';

import { Button } from 'components/Button';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';

import { cn } from '../../../../utils/shadcn';

export interface ProcessedAttachment {
  data: string;
  fileName: string;
  mimeType: string;
  size: number;
  originalFile: File;
}

interface AttachmentUploaderProps {
  onFilesChange: (files: ProcessedAttachment[]) => void;
  maxFileSize?: number;
}

const formatBytes = (bytes: number) => {
  if (bytes < 1024) return `${bytes} bytes`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  if (bytes < 1024 * 1024 * 1024) {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  }

  return `${(bytes / 1024 / 1024 / 1024).toFixed(2)} GB`;
};

// Helper function to convert File to base64
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const base64String = reader.result as string;
      // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
      const base64Content = base64String.split(',')[1];
      resolve(base64Content);
    };
    reader.onerror = (error) => reject(error);
  });
};

// Process a file to create a ProcessedAttachment
const processFile = async (file: File): Promise<ProcessedAttachment> => {
  const base64Data = await fileToBase64(file);
  return {
    data: base64Data,
    fileName: file.name,
    mimeType: file.type || 'application/octet-stream',
    size: file.size,
    originalFile: file,
  };
};

const AttachmentUpload: React.FC<AttachmentUploaderProps> = ({
  onFilesChange,
  maxFileSize = 25, // MB
}) => {
  const [attachedFiles, setAttachedFiles] = useState<ProcessedAttachment[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleDrop = useCallback(
    async (acceptedFiles: FileWithPath[]) => {
      if (acceptedFiles.length === 0) return;

      setIsProcessing(true);
      try {
        const processedFiles = await Promise.all(
          acceptedFiles.map((file) => processFile(file))
        );

        const newFiles = [...attachedFiles, ...processedFiles];
        setAttachedFiles(newFiles);
        onFilesChange(newFiles);
      } catch (error) {
        console.error('Error processing files:', error);
      } finally {
        setIsProcessing(false);
      }
    },
    [attachedFiles, onFilesChange]
  );

  const handleRemoveFile = (fileToRemove: ProcessedAttachment) => {
    const newFiles = attachedFiles.filter((file) => file !== fileToRemove);
    setAttachedFiles(newFiles);
    onFilesChange(newFiles);
  };

  const { getRootProps, getInputProps, isDragActive, fileRejections } =
    useDropzone({
      onDrop: handleDrop,
      validator: (file: File) => {
        if (file.size > maxFileSize * 1024 * 1024) {
          return {
            code: 'file-too-large',
            message: `File exceeds maximum size of ${maxFileSize}MB`,
          };
        }
        return null;
      },
    });

  const uploadMessage = useMemo(() => {
    if (isDragActive) return 'Drop files here ...';
    if (isProcessing) return 'Processing files...';
    if (attachedFiles.length > 0) {
      return 'Select more files or drag and drop to upload';
    }

    return 'Select files or drag and drop to upload';
  }, [isDragActive, isProcessing, attachedFiles.length]);

  return (
    <div className='flex flex-col gap-2'>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded py-3 px-5 text-center cursor-pointer',
          isDragActive && 'border-primary bg-primary/10',
          isProcessing && 'border-gray-300 bg-gray-100',
          !isDragActive &&
            !isProcessing &&
            'border-gray-300 hover:border-primary/50 hover:bg-primary/5'
        )}
        aria-label='File drop zone'
        tabIndex={0}
      >
        <input {...getInputProps()} aria-hidden='true' />
        <div className='flex flex-col gap-1'>
          <p>{uploadMessage}</p>
          <p className='text-xs text-grayscale-content-2'>
            Max file size: {maxFileSize}MB
          </p>
        </div>
      </div>
      {fileRejections.length > 0 && (
        <div className='text-red-500 text-xs'>
          {fileRejections.map((rejection: FileRejection, index: number) => (
            <p key={index}>
              {rejection.file.name}: {rejection.errors[0].message}
            </p>
          ))}
        </div>
      )}
      {attachedFiles.length > 0 && (
        <ul className='flex flex-col gap-1.5 mt-2'>
          {attachedFiles.map((file: ProcessedAttachment, index: number) => (
            <li
              key={index}
              className='flex items-center justify-between bg-gray-100 px-2.5 py-1.5 rounded border border-gray-300'
            >
              <div className='flex flex-col gap-0.5'>
                <span className='text-xs'>{file.fileName}</span>
                <span className='text-[10px] text-grayscale-content-2'>
                  {formatBytes(file.size)}
                </span>
              </div>
              <Button
                buttonNamePosthog={ButtonNamePosthog.RemoveAttachment}
                variant='destructive'
                size='xs'
                type='button'
                onClick={() => handleRemoveFile(file)}
                aria-label={`Remove ${file.fileName}`}
                className='border-transparent hover:border-red-500'
              >
                <XIcon className='h-4 w-4' />
              </Button>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default AttachmentUpload;
