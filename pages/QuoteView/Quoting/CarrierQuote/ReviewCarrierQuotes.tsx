import { useC<PERSON>back, useContext, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import _ from 'lodash';
import { CheckIcon, ChevronDown, ChevronUp, CopyIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { CarrierQuoteCard } from 'components/CarrierQuoteCard';
import { Label } from 'components/Label';
import { Textarea } from 'components/Textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import ButtonLoader from 'components/loading/ButtonLoader';
import CarrierPriceCalculator, {
  CarrierPriceCalculatorParent,
} from 'components/pricing/CarrierPriceCalculator';
import { ExtendedFormProvider } from 'contexts/extendedFormContext';
import {
  DrumkitPlatform,
  SidebarStateContext,
} from 'contexts/sidebarStateContext';
import { useToast } from 'hooks/useToaster';
import { updateQuoteRequestSuggestion } from 'lib/api/updateQuoteRequestSuggestion';
import { createMailClientInstance } from 'lib/mailclient/interface';
import { MappedQuote } from 'pages/QuoteView/Quoting/CarrierQuote/types';
import { CarrierNetworkEmail, CarrierQuote } from 'types/QuoteRequest';
import { Maybe, MaybeUndef } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import ButtonText from 'types/enums/ButtonText';
import { SuggestionStatus } from 'types/suggestions/LoadSuggestions';
import captureException from 'utils/captureException';
import { copyToClipboard } from 'utils/copyToClipboard';
import { formatCurrency } from 'utils/formatCurrency';
import { calculatePricing } from 'utils/priceCalculations';
import { cn } from 'utils/shadcn';

import { CarrierCostType, ProfitType } from '../RequestQuickQuote/types';

interface ReviewCarrierQuotesSectionProps {
  requestId: number;
  emails: CarrierNetworkEmail[];
  carrierQuotes: CarrierQuote[];
}

const MAX_QUOTES_DISPLAYED = 6;

export default function ReviewCarrierQuotesSection({
  requestId,
  emails,
  carrierQuotes,
}: ReviewCarrierQuotesSectionProps) {
  const { toast } = useToast();
  const [sortedQuotes, setSortedQuotes] = useState<MappedQuote[]>([]);
  const [selectedQuoteIndex, setSelectedQuoteIndex] = useState<number | null>(
    null
  );
  const [carrierCost, setCarrierCost] = useState(0);
  const [profit, setProfit] = useState(10);
  const [profitType, setProfitType] = useState<ProfitType>(
    ProfitType.Percentage
  );
  const [finalPrice, setFinalPrice] = useState<Maybe<number>>(
    carrierCost * (1 + profit / 100)
  );
  const [isCarrierButtonClicked, setIsCarrierButtonClicked] = useState(true);
  const [draftResponseBody, setDraftResponseBody] = useState('');
  const [hasCopiedDraftResponse, setHasCopiedDraftResponse] = useState(false);
  const [loadingDraftReply, setLoadingDraftReply] = useState(false);
  const [showAllCarrierQuotes, setShowAllCarrierQuotes] = useState(false);

  const {
    currentState: { drumkitPlatform, threadItemId, isOutlookReply },
  } = useContext(SidebarStateContext);

  const formMethods = useForm({
    defaultValues: {},
  });

  // TODO: Use Gmail/Outlook compose mode
  const draftMessage = useCallback(() => {
    setDraftResponseBody(
      `Hey,\n\nThanks for reaching out to us! We're able to do this shipment for ${formatCurrency(finalPrice ?? 0, 'USD')}.`
    );
  }, [finalPrice]);

  useEffect(() => {
    // Map quotes based on email recipients
    const unsortedQuotes: MappedQuote[] = emails?.map((email) => {
      const q = carrierQuotes.find((q) => q.threadID === email.threadID);
      return {
        carrierEmails: email.recipients,
        threadID: email.threadID,
        bounced: email.bounced,
        responded: !!q && !!q.emailID, // Check if q is truthy (i.e., if quote is found)
        available: q?.suggestedQuote?.isAvailable,
        price: q?.suggestedQuote?.totalCost,
        notes: q?.suggestedQuote?.notes,
        url: email.url,
      };
    });

    if (!unsortedQuotes) {
      return;
    }

    // Sort the Quotes array by availability and price
    const sortedQuotes = unsortedQuotes?.sort((a, b) => {
      // If one of the emails bounced, place it at the bottom
      if (a.bounced && !b.bounced) return 1;
      if (!a.bounced && b.bounced) return -1;

      // If one of the carriers did not respond, place it at the bottom
      if (!a.responded && b.responded) return 1;
      if (a.responded && !b.responded) return -1;

      // If one of the carriers is unavailable, place it at the bottom
      if (!a.available && b.available) return 1;
      if (a.available && !b.available) return -1;

      // If both carriers responded and are available, sort by price
      return (a.price || 0) - (b.price || 0);
    });

    setSortedQuotes(sortedQuotes);
    if (sortedQuotes.length > 0 && sortedQuotes[0].price) {
      setSelectedQuoteIndex(0);
      setCarrierCost(sortedQuotes[0].price);
    }
  }, [emails, carrierQuotes]);

  // Set body first time sidebar is opened to this section
  useEffect(() => {
    if (isCarrierButtonClicked) {
      draftMessage();
    }
  }, []);

  // Update draft message when final price changes
  useEffect(() => {
    draftMessage();
  }, [finalPrice, draftMessage]);

  const onSelectCarrier = (index: number) => {
    if (sortedQuotes[index].price === 0) {
      return;
    }

    setSelectedQuoteIndex(index);
    setIsCarrierButtonClicked(true);
    setCarrierCost(sortedQuotes[index].price ?? 0);
  };

  const onBlur = () => {
    draftMessage();
  };

  const handleViewThread = (url: MaybeUndef<string>) => {
    if (!url) {
      return;
    }

    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleCopyToClipboard = async () => {
    try {
      const success = await copyToClipboard(draftResponseBody);
      if (success) {
        setHasCopiedDraftResponse(true);
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedDraftResponse(false), 2000);
      }
    } catch {
      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  let handleDraftResponse: () => void;
  if (drumkitPlatform === DrumkitPlatform.Outlook) {
    const mailClient = createMailClientInstance(drumkitPlatform);
    handleDraftResponse = async () => {
      if (!finalPrice || finalPrice <= 0) {
        return;
      }

      setLoadingDraftReply(true);

      try {
        // Convert newlines to HTML <br> tags since the mail client works with HTML.
        const formattedDraftBody = draftResponseBody
          .trim()
          .replace(/\n/g, '<br>');

        await mailClient.draftReply({
          threadItemId,
          draftBody: formattedDraftBody,
        });

        // When in Read View, there's a lag between when Outlook created the draft in the backend
        // and showing it in the client so wait for a moment before showing toaster.
        setTimeout(
          () => {
            toast({
              description: 'Successfully created draft reply.',
              variant: 'success',
            });

            setLoadingDraftReply(false);
          },
          isOutlookReply ? 1 : 3500
        );
      } catch (error: unknown) {
        // narrow type to safely access the message
        const errMsg = error instanceof Error ? error.message : String(error);

        toast({
          description: errMsg || 'Something went wrong creating draft reply',
          variant: 'destructive',
        });

        setLoadingDraftReply(false);
      }
    };
  }

  /* handleTerminatingAction should be called whenever user takes an action that "terminates" the quote request,
   * i.e. updates the QR's status from "inFlight" to 1 of 2 terminating statuses: "accepted" or "rejected".
   * As of 6/4/2025, this occurs when user clicks "Submit to Portal", "Submit to TMS," "Create Draft Reply",
   * or one of the copy buttons.
   * This function
   * 1) Updates BE quote request with final quote and margin values (critical for metrics)
   * 2) Sends user quote to service (if enabled)
   * TODO: Capture line haul in BE
   */
  const handleTerminatingAction = async () => {
    // Use the utility function for final price and margin calculations
    const { flatCarrierCost: finalFlatCarrierCost, finalProfit } =
      calculatePricing(
        carrierCost,
        CarrierCostType.Flat,
        profit,
        profitType,
        0
      );

    // if (isQuoteSubmissionToServiceEnabled) {
    // }

    // TODO: Remove marginType and use profitType instead
    const result = await updateQuoteRequestSuggestion(
      requestId,
      SuggestionStatus.Accepted,
      {
        finalQuotePrice: _.round(finalPrice ?? 0),
        finalMargin: _.round(finalProfit),
        marginType: profitType,
        finalCarrierCost: _.round(finalFlatCarrierCost),
        carrierCostType: CarrierCostType.Flat,
      }
    );

    if (result.isErr()) {
      captureException(result.error, {
        functionName: 'handleTerminatingAction',
      });
    }
  };

  if (sortedQuotes.length === 0) {
    return (
      <div className='mb-6'>
        <h2 className='text-md whitespace-nowrap text-grayscale-content-label font-semibold'>
          Review Carrier Responses
        </h2>
        <p className='text-sm text-grayscale-content-label'>
          No carrier responses to view.
        </p>
      </div>
    );
  }

  return (
    <ExtendedFormProvider aiDefaultValues={true}>
      <FormProvider {...formMethods}>
        <div>
          <p className='text-xs text-grayscale-content-label mb-1 italic'>
            View email threads and quotes. Respond to customer.
          </p>

          {/* Show carrier responses */}
          <div className='mb-6 grid grid-cols-1 mx-0 w-full gap-2 mt-2'>
            {(showAllCarrierQuotes
              ? sortedQuotes
              : sortedQuotes.slice(0, 6)
            ).map((quote, index) => {
              const carrierQuote = carrierQuotes.find(
                (q) => q.threadID === quote.threadID
              );

              return (
                // margin right because accordion width is smaller and cuts off the check icon when card selected
                <div
                  className='mr-1'
                  key={`quote-${quote.carrierEmails}-${index}`}
                >
                  <CarrierQuoteCard
                    name={carrierQuote?.carrierLocation.name}
                    city={carrierQuote?.carrierLocation.city}
                    state={carrierQuote?.carrierLocation.state}
                    emails={quote.carrierEmails}
                    price={quote.price || undefined}
                    available={quote.available ?? undefined}
                    notes={quote.notes}
                    responded={quote.responded}
                    bounced={quote.bounced}
                    selected={selectedQuoteIndex === index}
                    onViewThread={() => handleViewThread(quote.url)}
                    onSelect={() => onSelectCarrier(index)}
                  />
                </div>
              );
            })}
            {sortedQuotes.length > 6 && (
              <button
                type='button'
                onClick={(e) => {
                  e.preventDefault();
                  setShowAllCarrierQuotes(!showAllCarrierQuotes);
                }}
                className='text-primary text-xs flex items-center gap-1 cursor-pointer hover:underline focus:outline-none pb-0.5'
              >
                {showAllCarrierQuotes
                  ? 'Show less'
                  : `Show ${sortedQuotes.length - MAX_QUOTES_DISPLAYED} more`}
                {showAllCarrierQuotes ? (
                  <ChevronUp size={14} />
                ) : (
                  <ChevronDown size={14} />
                )}
              </button>
            )}
          </div>

          {/* Calculate price to shipper */}
          <h2 className='my-1 text-sm font-medium'>Calculate Final Price:</h2>

          <CarrierPriceCalculator
            parentQuoteRequestId={requestId}
            maxDistance={0}
            showTitle={false}
            mileage={0} // Not using mileage since we're only supporting flat rates
            finalPrice={finalPrice}
            fuelEstimate={0}
            datFuelSurcharge={0}
            portalFuelSurcharge={null}
            portalFuelSurchargeSource={null}
            mileageSource={null}
            profit={profit}
            profitType={profitType}
            setProfitTypeHandler={setProfitType}
            carrierCost={carrierCost}
            carrierCostType={CarrierCostType.Flat}
            terminatingActionHandler={handleTerminatingAction}
            setCarrierCostTypeHandler={() => {}} // No-op since we're only supporting flat rates
            setCarrierCostHandler={setCarrierCost}
            setProfitHandler={setProfit}
            setFinalPriceHandler={setFinalPrice}
            setIsCarrierButtonClickedHandler={setIsCarrierButtonClicked}
            setSelectedQuoteIndexHandler={setSelectedQuoteIndex}
            onBlurHandler={onBlur}
            calculatorParent={CarrierPriceCalculatorParent.CarrierQuote}
          />

          <div className='mt-4'>
            <Label name='' className='text-sm'>
              Draft Response to Customer
            </Label>
            <div className='relative'>
              <Button
                buttonNamePosthog={ButtonNamePosthog.CopyDraftResponse}
                className={cn(
                  'absolute h-6 w-6 p-0 -top-6 right-0 border-none',
                  hasCopiedDraftResponse ? 'cursor-default' : 'cursor-pointer'
                )}
                variant='ghost'
                type='button'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  !hasCopiedDraftResponse && handleCopyToClipboard();
                }}
              >
                {hasCopiedDraftResponse ? (
                  <Tooltip open={true}>
                    <TooltipTrigger asChild>
                      <CheckIcon className='h-4 w-4' />
                    </TooltipTrigger>
                    <TooltipContent>Copied!</TooltipContent>
                  </Tooltip>
                ) : (
                  <CopyIcon className='h-4 w-4' />
                )}
              </Button>
              <Textarea
                name='draftResponse'
                className='p-2 h-36 mt-1'
                value={draftResponseBody}
                onChange={(e) => {
                  setDraftResponseBody(e.target.value);
                }}
              />
            </div>
          </div>
          {/* TODO Sophie: (nice to have) send email reply to customer/initials ender in gmail */}
          {/* TODO: Signpost user to build load and redirect to tab*/}

          {drumkitPlatform === DrumkitPlatform.Outlook && (
            <Button
              className='w-full h-8 text-sm mt-4'
              type='button'
              buttonNamePosthog={
                isOutlookReply
                  ? ButtonNamePosthog.AddReplyToCurrentDraftToCustomer
                  : ButtonNamePosthog.CreateDraftReplyToCustomer
              }
              disabled={loadingDraftReply}
              onClick={() => handleDraftResponse()}
            >
              {isOutlookReply ? (
                ButtonText.AddReplyToCurrentDraft
              ) : loadingDraftReply ? (
                <ButtonLoader />
              ) : (
                ButtonText.CreateDraftReply
              )}
            </Button>
          )}
        </div>
      </FormProvider>
    </ExtendedFormProvider>
  );
}
