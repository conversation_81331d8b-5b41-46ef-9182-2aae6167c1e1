import { useEffect, useState } from 'react';

import { ClipboardList, MailIcon } from 'lucide-react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/Accordion';
import ErrorBoundary from 'components/ErrorBoundary';
import { TooltipProvider } from 'components/Tooltip';
import SidebarLoader from 'components/loading/SidebarLoader';
import useLogPostHogPageView from 'hooks/useLogPostHogPageView';
import { useServiceFeatures } from 'hooks/useServiceContext';
import RequestCarrierQuotesByGroup from 'pages/QuoteView/Quoting/CarrierQuote/RequestCarrierQuotesByGroup';
import RequestCarrierQuotesByLocation from 'pages/QuoteView/Quoting/CarrierQuote/RequestCarrierQuotesByLocation';
import ReviewCarrierQuotesSection from 'pages/QuoteView/Quoting/CarrierQuote/ReviewCarrierQuotes';
import { Email } from 'types/Email';
import { QuoteRequest } from 'types/QuoteRequest';
import { Maybe, Undef } from 'types/UtilityTypes';
import Pageview from 'types/enums/Pageview';

enum CarrierQuoteSections {
  RequestQuotes = 'request-quotes',
  ReviewQuotes = 'review-quotes',
}

interface CarrierQuoteTabProps {
  email: Maybe<Email>;
  request: Maybe<QuoteRequest>;
  isLoadingRequest: boolean;
}

export default function CarrierQuoteTab({
  email,
  request,
  isLoadingRequest,
}: CarrierQuoteTabProps) {
  const { serviceID, carrierQuoteConfig } = useServiceFeatures();

  useLogPostHogPageView(Pageview.CarrierQuote, {
    service_id: serviceID,
    email_id: email?.id || null,
  });

  const [accordionTab, setAccordionTab] =
    useState<Undef<CarrierQuoteSections[]>>(undefined);

  useEffect(() => {
    if (isLoadingRequest) {
      return;
    }
    if (!request) {
      setAccordionTab([CarrierQuoteSections.RequestQuotes]);
      return;
    }

    if (
      !request.carrierNetworkEmails ||
      request.carrierNetworkEmails.length === 0
    ) {
      setAccordionTab([CarrierQuoteSections.RequestQuotes]);
      return;
    }

    setAccordionTab([CarrierQuoteSections.ReviewQuotes]);
  }, [request, isLoadingRequest]);

  if (!carrierQuoteConfig) {
    return (
      <div className='p-4 text-center'>
        No Carrier Quote configuration found. Please contact the Drumkit team
        for help.
      </div>
    );
  }

  return (
    <div className=''>
      <TooltipProvider>
        {isLoadingRequest && <SidebarLoader />}
        <Accordion
          type='multiple'
          value={accordionTab}
          onValueChange={(v) => setAccordionTab(v as CarrierQuoteSections[])}
        >
          {!isLoadingRequest && (
            <div>
              {/* Email Carriers Section */}
              <AccordionItem value={CarrierQuoteSections.RequestQuotes}>
                <AccordionTrigger
                  icon={<MailIcon className='h-6 w-6' strokeWidth={1} />}
                >
                  Email Carriers
                </AccordionTrigger>
                <AccordionContent>
                  <ErrorBoundary>
                    {carrierQuoteConfig.isFindCarrierByLocationEnabled && (
                      <RequestCarrierQuotesByLocation
                        email={email}
                        request={request}
                        carrierQuoteConfig={carrierQuoteConfig}
                      />
                    )}
                    {carrierQuoteConfig.isFindCarrierByGroupEnabled && (
                      <RequestCarrierQuotesByGroup
                        email={email}
                        request={request}
                        carrierQuoteConfig={carrierQuoteConfig}
                      />
                    )}
                  </ErrorBoundary>
                </AccordionContent>
              </AccordionItem>

              {/* Review Carrier Responses Section */}
              {!isLoadingRequest &&
                request &&
                request.carrierNetworkEmails &&
                request.carrierNetworkEmails.length > 0 && (
                  <AccordionItem value={CarrierQuoteSections.ReviewQuotes}>
                    <AccordionTrigger
                      icon={
                        <ClipboardList className='h-6 w-6' strokeWidth={1} />
                      }
                    >
                      Review Responses
                    </AccordionTrigger>
                    <AccordionContent>
                      <ErrorBoundary>
                        <ReviewCarrierQuotesSection
                          requestId={request.id}
                          emails={request.carrierNetworkEmails}
                          carrierQuotes={request.carrierQuotes ?? []}
                        />
                      </ErrorBoundary>
                    </AccordionContent>
                  </AccordionItem>
                )}
            </div>
          )}
        </Accordion>
      </TooltipProvider>
    </div>
  );
}
