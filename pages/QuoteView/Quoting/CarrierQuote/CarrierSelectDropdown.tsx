import { Info } from 'lucide-react';

import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { TMSLocationWithDistance } from 'types/Load';

interface CarrierSelectDropdownProps {
  searchResults: TMSLocationWithDistance[];
  onCarrierSelect: (carrierId: string) => void;
  isOpen: boolean;
}

export default function CarrierSelectDropdown({
  searchResults,
  onCarrierSelect,
  isOpen,
}: CarrierSelectDropdownProps) {
  if (!isOpen || searchResults.length === 0) {
    return null;
  }

  const handleCarrierClick = (carrierId: string) => {
    onCarrierSelect(carrierId);
  };

  return (
    <div
      role='listbox'
      aria-label='Carrier search results'
      className='absolute top-full left-0 right-0 mt-1 z-10 bg-white border border-grayscale-border-divider rounded-[4px] shadow-lg max-h-80 overflow-y-auto'
    >
      {searchResults.map((searchResult, index) => {
        const isFirst = index === 0;
        const isLast = index === searchResults.length - 1;
        let classNames =
          'p-2 hover:bg-primary/10 border-y border-gray-200 hover:border-primary cursor-pointer text-xs';
        if (isFirst) {
          classNames += ' border-t-0';
        }
        if (isLast) {
          classNames += ' border-b-0';
        }

        return (
          <div
            key={searchResult.externalTMSID}
            role='option'
            aria-selected='false'
            tabIndex={0}
            onClick={() => {
              if (searchResult.externalTMSID) {
                handleCarrierClick(searchResult.externalTMSID);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                if (searchResult.externalTMSID) {
                  handleCarrierClick(searchResult.externalTMSID);
                }
              }
            }}
            className={classNames}
          >
            <div className='flex justify-between items-start'>
              <div className='flex-grow'>
                <div className='font-medium text-sm'>
                  {searchResult.carrier?.name || searchResult.name}
                </div>
                {(searchResult.city || searchResult.state) && (
                  <div className='text-xs text-grayscale-content-tertiary'>
                    {searchResult.city}
                    {searchResult.city && searchResult.state ? ', ' : ''}
                    {searchResult.state}
                  </div>
                )}
              </div>
              {typeof searchResult.milesDistance === 'number' &&
                searchResult.milesDistance > 0 && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className='flex items-center text-xs text-grayscale-content-secondary ml-2 flex-shrink-0'>
                        <span>{searchResult.milesDistance.toFixed(1)} mi</span>
                        <Info
                          size={12}
                          className='ml-1 text-grayscale-icon-secondary group-hover:text-grayscale-icon-primary'
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side='top' align='end'>
                      <p>Approx. distance from pickup</p>
                    </TooltipContent>
                  </Tooltip>
                )}
            </div>

            <div className='text-grayscale-content-secondary mt-0.5'>
              {searchResult.emails?.join(', ') || searchResult.email}
            </div>
            {searchResult.notes && (
              <div
                className='mt-1 text-xs text-grayscale-content-input italic '
                title={searchResult.notes}
              >
                Note: {searchResult.notes}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
