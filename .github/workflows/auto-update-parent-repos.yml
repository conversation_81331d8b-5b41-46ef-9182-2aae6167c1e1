---
# A workflow to automatically update Alexandria's commit in parent repos IFF the same branch doesn't already exist in the parent repo.
# Add the "disable-auto-update" label to an Alexandria PR to skip this workflow.
#
# Note that to avoid duplicate PRs, developers are encouraged to 1) use the same (Linear) branch for related PRs across services,
# and 2) push at least once to the remote branch. These are generally good practices that we've already been applying.

name: Auto-Update Alexandria in Parent Repos

on:
  pull_request:
    branches: [main]
    types: [closed] # https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#pull_request

  workflow_dispatch:

jobs:
  update-alexandria:
    name: Update
    if: github.event.pull_request.merged && !contains(github.event.pull_request.labels.*.name, 'disable-auto-update') # https://stackoverflow.com/a/67833464
    runs-on: ubuntu-latest
    env:
      TOKEN: ${{ secrets.BOT_RW_PAT }}
      BASE_BRANCH: 'main' # For dev, create new branch and copy main's protections. Add that new branch to parent repo's 'on: pull request' workflow file(s)
      CHECKOUT_BRANCH: 'main' # Typically same as BASE_BRANCH
      LABELS: automated pr, # automerge
      ALEXANDRIA_PR_AUTHOR: ${{ github.actor }}
      ALEXANDRIA_BRANCH: ${{ github.head_ref }} # https://stackoverflow.com/a/59780579
      ALEXANDRIA_PR_TITLE: ${{ github.event.pull_request.title }}

    steps:
      - name: Pre-Vulcan checkout
        id: checkout
        uses: actions/checkout@v3

      - name: Update Vulcan
        id: vulcan
        uses: ./.github/actions/update-alexandria-in-vulcan # https://docs.github.com/en/actions/learn-github-actions/finding-and-customizing-actions
        with:
          parent_repository: drumkitai/vulcan
          github_token: ${{ env.TOKEN }}
          checkout_branch: ${{ env.CHECKOUT_BRANCH }}
          base_branch: ${{ env.BASE_BRANCH }}
          alexandria_pr_author: ${{ env.ALEXANDRIA_PR_AUTHOR }}
          alexandria_pr_title: ${{ env.ALEXANDRIA_PR_TITLE }}
          alexandria_branch: ${{ env.ALEXANDRIA_BRANCH }}
          labels: ${{ env.LABELS }}

      - name: Pre-Vesta checkout
        uses: actions/checkout@v3

      - name: Update Vesta
        id: vesta
        uses: ./.github/actions/update-alexandria-in-parents
        with:
          parent_repository: drumkitai/vesta
          github_token: ${{ env.TOKEN }}
          checkout_branch: ${{ env.CHECKOUT_BRANCH }}
          base_branch: ${{ env.BASE_BRANCH }}
          alexandria_pr_author: ${{ env.ALEXANDRIA_PR_AUTHOR }}
          alexandria_branch: ${{ env.ALEXANDRIA_BRANCH }}
          labels: ${{ env.LABELS }}

      - name: Pre-Verona checkout
        uses: actions/checkout@v3

      - name: Update Verona
        id: verona
        uses: ./.github/actions/update-alexandria-in-parents
        with:
          parent_repository: drumkitai/verona
          github_token: ${{ env.TOKEN }}
          checkout_branch: ${{ env.CHECKOUT_BRANCH }}
          base_branch: ${{ env.BASE_BRANCH }}
          alexandria_pr_author: ${{ env.ALEXANDRIA_PR_AUTHOR }}
          alexandria_branch: ${{ env.ALEXANDRIA_BRANCH }}
          labels: ${{ env.LABELS }}
          
      - name: Checkout Alexandria repo
        if: always()
        uses: actions/checkout@v3
# Future considerations:
# 1. Auto-merge once checks pass, don't wait for human approval (low-priority). See https://github.com/pascalgn/automerge-action
# 2. Put action in public repo so that we can call it without having to checkout the Alexandria repo multiple times (low-priority)
#    The cost of these these extra checkouts is marginal, but would reduce a bit of redundancy.
#    See https://docs.github.com/en/actions/learn-github-actions/finding-and-customizing-actions#adding-an-action-from-a-different-repository
