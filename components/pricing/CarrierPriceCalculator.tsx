import {
  FocusEventHandler,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';

import { captureException } from '@sentry/browser';
import _ from 'lodash';
import {
  CheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CopyIcon,
  InfoIcon,
} from 'lucide-react';

import KitDefaultProfitTooltip from 'components/AISuggestions/KitDefaultProfitTooltip';
import { Button } from 'components/Button';
import { Label } from 'components/Label';
import { Tooltip, TooltipContent, TooltipTrigger } from 'components/Tooltip';
import { Input } from 'components/input';
import { SidebarStateContext } from 'contexts/sidebarStateContext';
import { useServiceFeatures } from 'hooks/useServiceContext';
import { toast } from 'hooks/useToaster';
import { getFuelPriceEIA } from 'lib/api/getFuelPriceEIA';
import {
  DistanceSource,
  FuelType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/QuickQuoteForm';
import {
  CarrierCostType,
  ProfitType,
} from 'pages/QuoteView/Quoting/RequestQuickQuote/types';
import { Maybe } from 'types/UtilityTypes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import { QuotingPortals, integrationNameMap } from 'types/enums/Integrations';
import { copyToClipboard } from 'utils/copyToClipboard';
import { cn } from 'utils/shadcn';

export enum CarrierPriceCalculatorParent {
  QuickQuote = 'QuickQuote',
  CarrierQuote = 'CarrierQuote',
}

export type CarrierPriceCalculatorProps = {
  parentQuoteRequestId: number;
  showTitle: boolean;
  mileage: number;
  mileageSource: Maybe<DistanceSource>;
  finalPrice: Maybe<number>;
  fuelEstimate: number;
  portalFuelSurchargeSource: Maybe<QuotingPortals>;
  portalFuelSurcharge: Maybe<number>; // FSC AI-parsed from bidding portals like Freightview, E2Open, etc.
  datFuelSurcharge: Maybe<number>;
  profit: number;
  profitType: ProfitType;
  maxDistance: number;
  setProfitTypeHandler: React.Dispatch<React.SetStateAction<ProfitType>>;
  carrierCost: number;
  carrierCostType: CarrierCostType;
  terminatingActionHandler: () => Promise<void>;
  setCarrierCostTypeHandler: React.Dispatch<
    React.SetStateAction<CarrierCostType>
  >;
  onBlurHandler?: FocusEventHandler<HTMLInputElement>;
  setIsCarrierButtonClickedHandler?: React.Dispatch<
    React.SetStateAction<boolean>
  >;
  setSelectedQuoteIndexHandler?: React.Dispatch<
    React.SetStateAction<number | null>
  >;
  setCarrierCostHandler?: React.Dispatch<React.SetStateAction<number>>;
  setProfitHandler?: React.Dispatch<React.SetStateAction<number>>;
  setFuelEstimateHandler?: React.Dispatch<React.SetStateAction<number>>;
  setFinalPriceHandler?: React.Dispatch<React.SetStateAction<Maybe<number>>>;
  selectedQuickQuoteId?: number;
  calculatorParent: CarrierPriceCalculatorParent;
};

// Add your dev domain to view alternative labels
// TODO: Add settings page to Drumkit Portal
const altLabelsDomains = ['fetchfreight.com'];

// TIP: Do NOT use _.round() on any of the intermediate *numeric* values in this component as this causes precision issues
// when switching from flat to per mile or vice versa.
// Instead, always perform numeric calculations using floating point arithmetic, then call formatCostByType() and formatMarginByType()
// to format the results as strings with the correct number of decimal places.
export default function CarrierPriceCalculator({
  parentQuoteRequestId,
  showTitle,
  mileage: initMileage,
  finalPrice,
  fuelEstimate,
  datFuelSurcharge,
  portalFuelSurchargeSource,
  portalFuelSurcharge,
  profit: initialProfit,
  profitType,
  setProfitTypeHandler,
  carrierCost,
  carrierCostType,
  terminatingActionHandler,
  setCarrierCostTypeHandler,
  onBlurHandler,
  setIsCarrierButtonClickedHandler,
  setSelectedQuoteIndexHandler,
  setCarrierCostHandler,
  setProfitHandler,
  setFuelEstimateHandler,
  setFinalPriceHandler,
  calculatorParent,
}: CarrierPriceCalculatorProps): JSX.Element {
  const {
    currentState: { inboxEmailAddress },
  } = useContext(SidebarStateContext);

  const {
    serviceFeaturesEnabled: {
      isFuelTypeToggleEnabled,
      isQuoteCalculatorMarginEnabled,
    },
    configurations: { defaultPriceMargin, defaultPriceMarginType },
    serviceID,
  } = useServiceFeatures();

  // Format input values as strings to avoid floating point errors
  const [mileage, setMileage] = useState(initMileage);
  const [buyInputValue, setBuyInputValue] = useState(
    formatCostByType(carrierCost, carrierCostType)
  );

  const [isDefaultProfit, setIsDefaultProfit] = useState(true);
  const [isDefaultProfitType, setIsDefaultProfitType] = useState(true);
  const [profitInputValue, setProfitInputValue] = useState(
    formatProfitByType(initialProfit, carrierCostType, profitType)
  );
  const [hasCopiedSellPrice, setHasCopiedSellPrice] = useState(false);
  const [hasCopiedLinehaulPrice, setHasCopiedLinehaulPrice] = useState(false);

  const [isFuelDetailsOpen, setIsFuelDetailsOpen] = useState(false);
  const [fuelPriceEIA, setFuelPriceEIA] = useState<number>(0);

  const [fuelSurcharge, setFuelSurcharge] = useState<number>(0.3);
  const [selectedFuelType, setSelectedFuelType] = useState<FuelType>(
    FuelType.None
  );

  const [highlightLinehaulCalculation, setHighlightLinehaulCalculation] =
    useState(false);
  const [linehaulPrice, setLinehaulPrice] = useState<number>(0);

  // For Posthog logging
  const priceCalculatorProperties = useMemo(() => {
    return {
      serviceID,
      parentQuoteRequestId,
      mileage,
      finalPrice,
      fuelEstimate,
      profit: initialProfit,
      profitType,
      carrierCost,
      carrierCostType,
      fuelSurcharge,
      selectedFuelType,
    };
  }, [
    serviceID,
    parentQuoteRequestId,
    mileage,
    finalPrice,
    fuelEstimate,
    initialProfit,
    profitType,
    carrierCost,
    carrierCostType,
    fuelSurcharge,
    selectedFuelType,
  ]);

  useEffect(() => {
    setBuyInputValue(formatCostByType(carrierCost, carrierCostType));
  }, [carrierCost, carrierCostType]);

  useEffect(() => {
    // Syncs profitInputValue when the initialProfit (from QQF.profit),
    // or the relevant types (carrierCostType from CPC state, profitType from QQF prop) change.
    setProfitInputValue(
      formatProfitByType(initialProfit, carrierCostType, profitType)
    );
  }, [initialProfit, carrierCostType, profitType]);

  // If a portal fuel surcharge is provided, open the fuel details panel
  useEffect(() => {
    if (portalFuelSurcharge) {
      setIsFuelDetailsOpen(true);
    }
  }, [portalFuelSurcharge]);

  useEffect(() => {
    const roundedInitMileage = _.round(initMileage);
    setMileage(roundedInitMileage);
  }, [initMileage]);

  useEffect(() => {
    if (carrierCostType === CarrierCostType.Flat) {
      setLinehaulPrice((finalPrice ?? 0) - fuelEstimate);
    } else {
      setLinehaulPrice((finalPrice ?? 0) - fuelSurcharge);
    }
  }, [finalPrice, fuelEstimate, fuelSurcharge, carrierCostType]);

  useEffect(() => {
    fetchEIAFuelPrice();
  }, []);

  useEffect(() => {
    // Default to DAT Fuel surcharge if toggle is enabled, otherwise default to DOE
    if (isFuelTypeToggleEnabled && datFuelSurcharge) {
      handleSelectedFuelToggle(FuelType.DAT);
      return;
    }

    if (portalFuelSurcharge) {
      handleSelectedFuelToggle(FuelType.Portal);
      return;
    }

    if (fuelPriceEIA) {
      handleSelectedFuelToggle(FuelType.DOE);
    }
  }, [
    datFuelSurcharge,
    fuelPriceEIA,
    portalFuelSurcharge,
    isFuelTypeToggleEnabled,
  ]);

  useEffect(() => {
    // If no default margin price or type is set, return early and use form's defaults
    if (!defaultPriceMargin || !defaultPriceMarginType) return;

    if (defaultPriceMarginType !== profitType) {
      handleProfitTypeToggle();
    }

    setProfitInputValue(
      formatProfitByType(
        defaultPriceMargin,
        carrierCostType,
        defaultPriceMarginType
      )
    );
  }, [defaultPriceMargin, defaultPriceMarginType]);

  useEffect(() => {
    setFuelEstimateHandler && setFuelEstimateHandler(mileage * fuelSurcharge);
  }, [mileage, fuelSurcharge]);

  const fetchEIAFuelPrice = async () => {
    const res = await getFuelPriceEIA();
    if (res.isOk()) {
      setFuelPriceEIA(res.value.fuelPrice);
    }
  };

  useEffect(() => {
    if (!setFinalPriceHandler) return;

    const numericProfitInput = Number(profitInputValue);

    if (!isNaN(carrierCost) && !isNaN(numericProfitInput)) {
      let calculatedFinalPrice = 0;

      if (profitType === ProfitType.Amount) {
        // Flat margin: Add the margin to the carrier cost
        calculatedFinalPrice = carrierCost + numericProfitInput;
      } else {
        // ProfitType.Percentage
        if (isQuoteCalculatorMarginEnabled) {
          // Margin formula: Price = Cost / (1 - Profit_Percentage)
          if (numericProfitInput >= 100) {
            // Profit margin cannot be 100% or more.
            // Using 99.9% as a practical upper limit to avoid division by zero or negative.
            calculatedFinalPrice = carrierCost / (1 - 0.999); // e.g. carrierCost / 0.001 = carrierCost * 1000
          } else {
            calculatedFinalPrice = carrierCost / (1 - numericProfitInput / 100);
          }
        } else {
          // Markup formula: Price = Cost * (1 + Markup_Percentage)
          calculatedFinalPrice = carrierCost * (1 + numericProfitInput / 100);
        }
      }
      setFinalPriceHandler(calculatedFinalPrice);
    }
  }, [
    profitInputValue,
    carrierCost,
    profitType,
    carrierCostType,
    isQuoteCalculatorMarginEnabled,
    setFinalPriceHandler,
  ]);

  const handleBuyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    setBuyInputValue(value);

    if (setSelectedQuoteIndexHandler) {
      setSelectedQuoteIndexHandler(null);
    }
    if (setCarrierCostHandler && value !== '') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setCarrierCostHandler(numValue);
      }
    }
    if (setIsCarrierButtonClickedHandler) {
      setIsCarrierButtonClickedHandler(false);
    }
  };

  const handleProfitChange = (value: string) => {
    setProfitInputValue(value);

    // Compare the current value with the default price margin
    setIsDefaultProfit(Number(value) === defaultPriceMargin);

    if (setProfitHandler && value !== '') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setProfitHandler(numValue);
      }
    }
  };

  const handleChangeMileage = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMileage(parseFloat(value));
  };

  const handleChangeFuelSurcharge = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    setFuelSurcharge(parseFloat(value));
  };

  const handleProfitTypeToggle = () => {
    const newProfitType =
      profitType === ProfitType.Amount
        ? ProfitType.Percentage
        : ProfitType.Amount;

    const currentNumericProfit = Number(profitInputValue);
    const currentCarrierCost = carrierCost;
    let currentFinalPrice = 0;

    // Determine the current finalPrice based on existing values and calculation mode
    if (profitType === ProfitType.Amount) {
      currentFinalPrice = currentCarrierCost + currentNumericProfit;
    } else {
      // ProfitType.Percentage
      if (isQuoteCalculatorMarginEnabled) {
        if (currentNumericProfit >= 100) {
          currentFinalPrice = currentCarrierCost / (1 - 0.999);
        } else {
          currentFinalPrice =
            currentCarrierCost / (1 - currentNumericProfit / 100);
        }
      } else {
        currentFinalPrice =
          currentCarrierCost * (1 + currentNumericProfit / 100);
      }
    }

    let newProfitValue: number;

    if (newProfitType === ProfitType.Percentage) {
      // Switching from Flat ($) to Percentage (%)
      // New Profit % = ((Final Price / Carrier Cost) - 1) * 100 for markup
      // New Profit % = (1 - (Carrier Cost / Final Price)) * 100 for margin
      if (currentCarrierCost > 0 && currentFinalPrice > 0) {
        if (isQuoteCalculatorMarginEnabled) {
          if (currentFinalPrice === currentCarrierCost) {
            // Avoid division by zero or negative if cost equals price (0% margin)
            newProfitValue = 0;
          } else if (currentFinalPrice < currentCarrierCost) {
            // Loss scenario, represent as negative margin
            newProfitValue = (1 - currentCarrierCost / currentFinalPrice) * 100; // will be negative
          } else {
            newProfitValue = (1 - currentCarrierCost / currentFinalPrice) * 100;
          }
        } else {
          newProfitValue = (currentFinalPrice / currentCarrierCost - 1) * 100;
        }
      } else {
        newProfitValue = 0; // Default to 0 if carrierCost or finalPrice is zero
      }
    } else {
      // Switching from Percentage (%) to Flat ($)
      // New Profit $ = Final Price - Carrier Cost
      newProfitValue = currentFinalPrice - currentCarrierCost;
    }

    const newProfitFormatted = formatProfitByType(
      newProfitValue,
      carrierCostType,
      newProfitType
    );

    setProfitInputValue(newProfitFormatted);
    setProfitHandler?.(newProfitValue);

    // Update whether new margin value matches default
    if (defaultPriceMarginType && defaultPriceMarginType === newProfitType) {
      setIsDefaultProfit(newProfitValue === defaultPriceMargin);
    }
    if (defaultPriceMarginType) {
      setIsDefaultProfitType(newProfitType === defaultPriceMarginType);
    }

    // Toggle the margin type
    setProfitTypeHandler(newProfitType);
  };

  const handleSelectedFuelToggle = (newType: FuelType) => {
    if (selectedFuelType === newType) return;

    setSelectedFuelType(newType);

    switch (newType) {
      case FuelType.DAT:
        setFuelSurcharge(datFuelSurcharge!);
        break;
      case FuelType.DOE:
        setFuelSurcharge(_.round(fuelPriceEIA * 0.1, 2)); // 10% of Diesel gallon price
        break;
      case FuelType.Portal:
        setFuelSurcharge(portalFuelSurcharge!);
        break;
    }
  };

  const handleCarrierCostTypeToggle = () => {
    if (buyInputValue) {
      const currentCarrierCost = carrierCost;
      const currentProfit = Number(profitInputValue);
      const newCarrierCostType =
        carrierCostType === CarrierCostType.Flat
          ? CarrierCostType.PerMile
          : CarrierCostType.Flat;

      let newCarrierCost: number;
      let newProfit = currentProfit;

      if (newCarrierCostType === CarrierCostType.PerMile) {
        // Convert from Flat to Per Mile
        newCarrierCost = currentCarrierCost / mileage;
        // Convert margin value when switching types
        if (profitType === ProfitType.Amount) {
          newProfit = currentProfit / mileage;
        }
      } else {
        // Convert from Per Mile to Flat
        newCarrierCost = currentCarrierCost * mileage;
        // Convert margin value when switching types
        if (profitType === ProfitType.Amount) {
          newProfit = currentProfit * mileage;
        }
      }

      setCarrierCostHandler?.(newCarrierCost);
      setProfitHandler?.(newProfit);
      setBuyInputValue(formatCostByType(newCarrierCost, newCarrierCostType));
      setProfitInputValue(
        formatProfitByType(newProfit, newCarrierCostType, profitType)
      );
      setCarrierCostTypeHandler(newCarrierCostType);
    }
  };

  const handleCopySellPriceToClipboard = async () => {
    if (!finalPrice) {
      toast({
        description: 'No quote to submit.',
        variant: 'info',
      });
      return;
    }

    const roundedFinalPrice = formatCostByType(finalPrice, carrierCostType);

    try {
      const success = await copyToClipboard(roundedFinalPrice);
      if (success) {
        setHasCopiedSellPrice(true);
        terminatingActionHandler();
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedSellPrice(false), 2000);
      }
    } catch (error) {
      captureException(error);

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  const handleCopyLinehaulToClipboard = async () => {
    const roundedLinehaulPrice = formatCostByType(
      linehaulPrice,
      carrierCostType
    );

    try {
      const success = await copyToClipboard(roundedLinehaulPrice);
      if (success) {
        setHasCopiedLinehaulPrice(true);
        // Use the utility function for final price and margin calculations
        terminatingActionHandler();
        // Reset copied state after a delay
        setTimeout(() => setHasCopiedLinehaulPrice(false), 2000);
      }
    } catch (error) {
      captureException(error);

      toast({
        description: 'Failed to copy to clipboard.',
        variant: 'destructive',
      });
    }
  };

  return (
    <>
      {showTitle && (
        <h2 className='mb-3 text-sm font-medium text-grayscale-content-description'>
          Calculate Final Price
        </h2>
      )}

      <div className='flex flex-col gap-2'>
        <div className='flex items-baseline gap-0 mb-2 xxs:gap-3'>
          {/* Carrier Cost */}
          <div className='flex flex-col gap-1 min-w-0'>
            <Label
              name='buy'
              className='text-grayscale-content-description !text-xs font-medium'
            >
              {altLabelsDomains.some((domain) =>
                inboxEmailAddress.toLowerCase().includes(domain.toLowerCase())
              )
                ? 'TTT'
                : 'Buy'}
            </Label>
            <div className='flex flex-col items-center gap-1'>
              {' '}
              <div className='flex items-center relative'>
                <span className='absolute left-1 xxs:left-2 text-sm text-grayscale-content-description'>
                  $
                </span>
                <Input
                  type='number'
                  value={buyInputValue}
                  onChange={handleBuyChange}
                  onBlur={onBlurHandler ?? undefined}
                  className='pl-4 pr-1 xxs:pr-2 xxs:pl-5 py-1.5 text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                  min={0}
                  step={carrierCostType === CarrierCostType.PerMile ? 0.01 : 1}
                  aria-label='Carrier cost input'
                />
                {carrierCostType === CarrierCostType.PerMile && (
                  <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-grayscale-content-description'>
                    /mi
                  </div>
                )}
              </div>
              {Boolean(mileage) && (
                <div className='flex rounded-[4px] overflow-hidden border border-grayscale-border-input text-xs'>
                  <button
                    type='button'
                    title={'Flat Rate'}
                    onClick={handleCarrierCostTypeToggle}
                    className={`px-2 transition-colors  ${
                      carrierCostType === CarrierCostType.Flat
                        ? 'text-[#FE9659] font-medium bg-orange-bg'
                        : 'text-grayscale-content-description hover:text-[#FE9659]'
                    }`}
                  >
                    Flat
                  </button>
                  <button
                    title={'Per Mile'}
                    type='button'
                    onClick={handleCarrierCostTypeToggle}
                    className={`px-2 transition-colors  ${
                      carrierCostType === CarrierCostType.PerMile
                        ? 'text-[#FE9659] font-medium bg-orange-bg'
                        : 'text-grayscale-content-description hover:text-[#FE9659]'
                    }`}
                  >
                    /mi
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Markup or Margin */}
          <div className='flex flex-col items-start gap-1 min-w-0'>
            <Label
              name='margin'
              className='text-grayscale-content-description !text-xs pl-4 font-medium'
            >
              {isQuoteCalculatorMarginEnabled ? 'Margin' : 'Markup'}
            </Label>
            <div className='flex flex-col items-center gap-1'>
              <div className='flex flex-row items-center'>
                <span className='mx-1 xxs:mx-0 xxs:mr-2 text-sm text-grayscale-content-description'>
                  +
                </span>
                <div className='relative flex items-center'>
                  {profitType === ProfitType.Amount && (
                    <div className='absolute top-1.5 left-1 xxs:left-2 text-sm text-grayscale-content-description'>
                      $
                    </div>
                  )}
                  <Input
                    type='number'
                    value={profitInputValue}
                    onChange={(e) => handleProfitChange(e.target.value)}
                    onBlur={onBlurHandler ?? undefined}
                    className={`min-w-[60px] text-sm pl-4 xxs:pl-5 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none`}
                    min={0}
                    step={
                      profitType === ProfitType.Percentage
                        ? 1
                        : carrierCostType === CarrierCostType.PerMile
                          ? 0.01
                          : 1
                    }
                    aria-label='Margin input'
                  />

                  <KitDefaultProfitTooltip
                    profitInputValue={profitInputValue}
                    profitInputType={profitType}
                    isDefaultProfit={isDefaultProfit}
                    isDefaultProfitType={isDefaultProfitType}
                    setIsDefaultProfit={setIsDefaultProfit}
                    setIsDefaultProfitType={setIsDefaultProfitType}
                  />

                  {profitType !== ProfitType.Amount && (
                    <div className='absolute top-1.5 right-1 xxs:right-2 text-sm text-grayscale-content-description'>
                      %
                    </div>
                  )}
                </div>
                <span className='mx-1 xxs:mx-0 xxs:ml-2 text-sm text-grayscale-content-description'>
                  =
                </span>
              </div>
              {/* Markup or Margin Type Toggle */}
              <div className='flex rounded-[4px] overflow-hidden border border-grayscale-border-input text-xs'>
                <button
                  type='button'
                  title={'Use Dollar Profit'}
                  onClick={handleProfitTypeToggle}
                  className={`px-2 transition-colors  ${
                    profitType === ProfitType.Amount
                      ? 'text-[#FE9659] font-medium bg-orange-bg'
                      : 'text-grayscale-content-description hover:text-[#FE9659]'
                  }`}
                >
                  $
                </button>
                <button
                  type='button'
                  title={'Use Percentage Profit'}
                  onClick={handleProfitTypeToggle}
                  className={`px-2 transition-colors  ${
                    profitType === ProfitType.Percentage
                      ? 'text-[#FE9659] font-medium bg-orange-bg'
                      : 'text-grayscale-content-description hover:text-[#FE9659]'
                  }`}
                >
                  %
                </button>
              </div>
            </div>
          </div>

          {/* Final Price */}
          <div className='flex flex-col gap-1'>
            <Label
              name='sell'
              className='text-grayscale-content-description !text-xs font-medium'
            >
              {altLabelsDomains.some((domain) =>
                inboxEmailAddress.toLowerCase().includes(domain.toLowerCase())
              )
                ? 'TTC'
                : 'Sell'}
            </Label>
            <div className='flex items-center relative'>
              <span className='absolute left-1 xxs:left-2 text-sm text-grayscale-content-description'>
                $
              </span>
              <Input
                className={cn(
                  'pl-4 xxs:pl-5 pr-2 py-1.5 transition-all text-sm max-xxs:min-w-[70px] read-only:bg-gray-50 read-only:text-grayscale-content-description read-only:border-grayscale-border-input',
                  highlightLinehaulCalculation &&
                    '!bg-green-bg !border-green-main'
                )}
                type='text'
                value={
                  isNaN(finalPrice ?? 0)
                    ? ''
                    : formatCostByType(finalPrice ?? 0, carrierCostType)
                }
                disabled
                aria-label='Final price'
              />
              {carrierCostType === CarrierCostType.PerMile && (
                <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-grayscale-content-description'>
                  /mi
                </div>
              )}

              <Button
                buttonNamePosthog={
                  calculatorParent === CarrierPriceCalculatorParent.QuickQuote
                    ? ButtonNamePosthog.QuickQuoteCopySellPrice
                    : ButtonNamePosthog.CarrierQuoteCopySellPrice
                }
                logProperties={priceCalculatorProperties}
                className={cn(
                  'absolute h-4 p-0 -top-5 right-1 border-none',
                  hasCopiedSellPrice ? 'cursor-default' : 'cursor-pointer'
                )}
                variant='ghost'
                type='button'
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  !hasCopiedSellPrice && handleCopySellPriceToClipboard();
                }}
              >
                {hasCopiedSellPrice ? (
                  <Tooltip open={true}>
                    <TooltipTrigger asChild>
                      <CheckIcon className='h-4 w-4' />
                    </TooltipTrigger>
                    <TooltipContent>Copied!</TooltipContent>
                  </Tooltip>
                ) : (
                  <CopyIcon className='h-4 w-4' />
                )}
              </Button>
            </div>

            {Boolean(mileage) && (
              <div className='flex justify-center align-baseline rounded-[4px] overflow-hidden border border-grayscale-border-input text-xs'>
                {/* Total price shown below when CarrierCostType is Per Mile */}
                {finalPrice && carrierCostType === CarrierCostType.PerMile && (
                  <p className='text-xs px-0  text-grayscale-content-description justify-self-center'>
                    {`Total:  `}
                    <span className='text-xs text-[#FE9659] font-medium bg-orange-bg'>
                      {`$${formatCostByType(finalPrice * mileage, CarrierCostType.Flat)}`}
                    </span>
                  </p>
                )}
                {/* Per-mile price shown below when CarrierCostType is Flat */}
                {finalPrice && carrierCostType === CarrierCostType.Flat && (
                  <span className='text-xs text-grayscale-content-description flex items-center gap-1'>
                    <span className='text-xs bg-orange-bg px-1 font-medium text-[#FE9659]'>
                      {`$${formatCostByType(finalPrice / mileage, CarrierCostType.PerMile)}`}
                    </span>
                    <span className='text-xs'>/mi</span>
                  </span>
                )}
              </div>
            )}

            {!finalPrice && <p className='text-red-500 text-xs '>Required</p>}
          </div>
        </div>

        {isFuelDetailsOpen && setFuelEstimateHandler && (
          <div>
            <div className='flex items-baseline gap-0 mb-2 xxs:gap-3'>
              {/* Mileage for Fuel */}
              <div className='flex flex-col gap-1 min-w-0'>
                <Label
                  name='mileage'
                  className='text-grayscale-content-description !text-xs font-medium'
                >
                  Distance
                </Label>
                <div className='flex flex-col items-center gap-1'>
                  {' '}
                  <div className='flex items-center relative'>
                    <Input
                      type='number'
                      value={mileage}
                      onChange={handleChangeMileage}
                      onBlur={onBlurHandler ?? undefined}
                      className='pl-2 pr-2 py-1.5 text-sm [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
                      min={0}
                      aria-label='Mileage input'
                    />
                    <div className='absolute bottom-1.5 right-1 xxs:right-2 text-xs text-grayscale-content-description'>
                      mi
                    </div>
                  </div>
                </div>
              </div>

              {/* Fuel Surcharge */}
              {/* TODO: Add AI icon */}
              <div className='flex flex-col items-start gap-1 min-w-0'>
                <Label
                  name='fsc'
                  className='w-full text-grayscale-content-description !text-xs px-4 font-medium'
                >
                  <div className='w-full flex justify-between items-center'>
                    <p>FSC</p>

                    <Tooltip delayDuration={10}>
                      <TooltipTrigger asChild>
                        <InfoIcon className='h-4 w-4' />
                      </TooltipTrigger>
                      <TooltipContent>
                        {selectedFuelType === FuelType.DOE && (
                          <div>
                            <p>10% of DOE per gallon price</p>
                            <p className='text-[12px] italic text-grayscale-content-3 mt-2'>
                              {`This week's DOE Diesel Price: $${fuelPriceEIA}/gallon`}
                            </p>
                          </div>
                        )}
                        {selectedFuelType === FuelType.Portal &&
                          portalFuelSurchargeSource && (
                            <p>
                              {`AI detected FSC from ${integrationNameMap[portalFuelSurchargeSource]}`}
                            </p>
                          )}
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </Label>

                <div className='flex flex-col items-center gap-1'>
                  <div className='flex flex-row items-center'>
                    <span className='mx-1 xxs:mx-0 xxs:mr-2 text-sm text-grayscale-content-description'>
                      *
                    </span>
                    <div className='relative flex items-center'>
                      <div className='absolute top-1.5 left-1 xxs:left-2 text-sm text-grayscale-content-description'>
                        $
                      </div>
                      <Input
                        type='number'
                        value={fuelSurcharge}
                        onChange={handleChangeFuelSurcharge}
                        onBlur={onBlurHandler ?? undefined}
                        className={cn(
                          'min-w-[60px] text-sm pl-4 xxs:pl-5 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none',
                          highlightLinehaulCalculation &&
                            carrierCostType === CarrierCostType.PerMile &&
                            '!bg-red-bg !border-red-main'
                        )}
                        min={0}
                        step={0.01}
                        aria-label='Fuel Surcharge input'
                      />
                    </div>
                    <span className='mx-1 xxs:mx-0 xxs:ml-2 text-sm text-grayscale-content-description'>
                      =
                    </span>
                  </div>
                </div>

                {isFuelTypeToggleEnabled &&
                  (datFuelSurcharge || portalFuelSurcharge) && (
                    <div className='flex mx-auto rounded-[4px] overflow-hidden border border-grayscale-border-input text-xs'>
                      <button
                        type='button'
                        title={'Use DOE Fuel Rate'}
                        onClick={() => handleSelectedFuelToggle(FuelType.DOE)}
                        className={`text-[10px] px-2 transition-colors  ${
                          selectedFuelType === FuelType.DOE
                            ? 'text-[#FE9659] font-medium bg-orange-bg'
                            : 'text-grayscale-content-description hover:text-[#FE9659]'
                        }`}
                      >
                        DOE
                      </button>
                      {datFuelSurcharge && (
                        <button
                          type='button'
                          title={'Use DAT Fuel Rate'}
                          onClick={() => handleSelectedFuelToggle(FuelType.DAT)}
                          className={cn(
                            'text-[10px] px-2 transition-colors',
                            selectedFuelType === FuelType.DAT
                              ? 'text-[#FE9659] font-medium bg-orange-bg'
                              : 'text-grayscale-content-description hover:text-[#FE9659]'
                          )}
                        >
                          DAT
                        </button>
                      )}
                      {portalFuelSurcharge && (
                        <button
                          type='button'
                          title={`Use ${
                            portalFuelSurchargeSource
                              ? integrationNameMap[portalFuelSurchargeSource]
                              : 'Portal'
                          } Fuel Rate`}
                          onClick={() =>
                            handleSelectedFuelToggle(FuelType.Portal)
                          }
                          className={cn(
                            'text-[10px] px-2 transition-colors',
                            selectedFuelType === FuelType.Portal
                              ? 'text-[#FE9659] font-medium bg-orange-bg'
                              : 'text-grayscale-content-description hover:text-[#FE9659]'
                          )}
                        >
                          {portalFuelSurchargeSource
                            ? integrationNameMap[portalFuelSurchargeSource]
                            : 'Portal'}
                        </button>
                      )}
                    </div>
                  )}
              </div>

              {/* Final Fuel Price */}
              <div className='flex flex-col gap-1'>
                <Label
                  name='totalFuel'
                  className='text-grayscale-content-description !text-xs font-medium'
                >
                  Fuel Estimate
                </Label>
                <div className='flex items-center relative'>
                  <span className='absolute left-1 xxs:left-2 text-sm text-grayscale-content-description'>
                    $
                  </span>
                  <Input
                    className={cn(
                      'pl-4 xxs:pl-5 pr-2 py-1.5 transition-all text-sm max-xxs:min-w-[70px] read-only:bg-gray-50 read-only:text-grayscale-content-description read-only:border-grayscale-border-input',
                      highlightLinehaulCalculation &&
                        carrierCostType === CarrierCostType.Flat &&
                        '!bg-red-bg !border-red-main'
                    )}
                    type='text'
                    value={
                      isNaN(mileage * fuelSurcharge)
                        ? ''
                        : formatCostByType(
                            mileage * fuelSurcharge,
                            CarrierCostType.Flat
                          )
                    }
                    disabled
                    aria-label='Final Fuel Price'
                  />
                </div>
              </div>
            </div>

            <div className='flex items-baseline mx-auto mt-4'>
              {/* Empty Distance column mock for spacing standards */}
              <div className='flex basis-1/3 mx-1 flex-col gap-1 max-w-[200px]' />
              {/* Empty FSC column mock for spacing standards */}
              <div className='flex basis-1/3 mx-1 flex-col items-start gap-1 max-w-[200px]' />

              <div className='flex basis-1/3 flex-col items-start gap-1 max-w-[200px] relative'>
                <Label
                  name='totalFuel'
                  className='text-grayscale-content-description !text-xs font-medium'
                >
                  Linehaul
                </Label>
                <div
                  className='flex items-center relative'
                  onMouseOver={() => setHighlightLinehaulCalculation(true)}
                  onMouseLeave={() => setHighlightLinehaulCalculation(false)}
                >
                  <span className='absolute left-1 text-sm text-grayscale-content-description'>
                    $
                  </span>
                  <Input
                    className='pl-4 pr-2 py-1.5 text-sm read-only:bg-gray-50 min-w-[90px] read-only:text-grayscale-content-description read-only:border-grayscale-border-input'
                    type='text'
                    value={
                      isNaN(linehaulPrice)
                        ? ''
                        : formatCostByType(linehaulPrice, carrierCostType)
                    }
                    disabled
                    aria-label='Linehaul'
                  />
                  {carrierCostType === CarrierCostType.PerMile && (
                    <div className='absolute bottom-1.5 right-1 text-xs text-grayscale-content-description'>
                      /mi
                    </div>
                  )}
                </div>

                {/* Show total linehaul or per-mile value below, styled like Sell price */}
                {Boolean(mileage) && (
                  <div className='flex justify-center align-baseline rounded-[4px] overflow-hidden border border-grayscale-border-input text-xs mt-1 w-full'>
                    {/* Total linehaul shown below when CarrierCostType is PerMile */}
                    {linehaulPrice &&
                      carrierCostType === CarrierCostType.PerMile && (
                        <p className='text-xs px-0 text-grayscale-content-description justify-self-center'>
                          {`Total:  `}
                          <span className='text-xs text-[#FE9659] font-medium bg-orange-bg'>
                            {`$${formatCostByType(linehaulPrice * mileage, CarrierCostType.Flat)}`}
                          </span>
                        </p>
                      )}
                    {/* Per-mile linehaul shown below when CarrierCostType is Flat */}
                    {linehaulPrice &&
                      carrierCostType === CarrierCostType.Flat && (
                        <span className='text-xs text-grayscale-content-description flex items-center gap-1'>
                          <span className='text-xs bg-orange-bg px-1 font-medium text-[#FE9659]'>
                            {`$${formatCostByType(linehaulPrice / mileage, CarrierCostType.PerMile)}`}
                          </span>
                          <span className='text-xs'>/mi</span>
                        </span>
                      )}
                  </div>
                )}

                <Button
                  buttonNamePosthog={
                    calculatorParent === CarrierPriceCalculatorParent.QuickQuote
                      ? ButtonNamePosthog.QuickQuoteCopyLinehaulPrice
                      : ButtonNamePosthog.CarrierQuoteCopyLinehaulPrice
                  }
                  logProperties={priceCalculatorProperties}
                  className={cn(
                    'absolute h-4 p-0 top-1 right-1 border-none',
                    hasCopiedLinehaulPrice ? 'cursor-default' : 'cursor-pointer'
                  )}
                  variant='ghost'
                  type='button'
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    !hasCopiedLinehaulPrice && handleCopyLinehaulToClipboard();
                  }}
                >
                  {hasCopiedLinehaulPrice ? (
                    <Tooltip open={true}>
                      <TooltipTrigger asChild>
                        <CheckIcon className='h-4 w-4' />
                      </TooltipTrigger>
                      <TooltipContent>Copied!</TooltipContent>
                    </Tooltip>
                  ) : (
                    <CopyIcon className='h-4 w-4' />
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {setFuelEstimateHandler && (
          <Button
            className='w-40 mx-auto h-8 text-[14px] text-grayscale-content-2 flex gap-2 hover:border-gray-600 hover:bg-gray-200'
            buttonNamePosthog={ButtonNamePosthog.ToggleQuoteFuelDetails}
            onClick={() => setIsFuelDetailsOpen(!isFuelDetailsOpen)}
            type='button'
            variant='ghost'
          >
            {!isFuelDetailsOpen ? (
              <ChevronDownIcon className='h-4 w-4' />
            ) : (
              <ChevronUpIcon className='h-4 w-4' />
            )}
            Adjust Fuel
          </Button>
        )}
      </div>
    </>
  );
}

export const formatCostByType = (value: number, type: CarrierCostType) => {
  if (type === CarrierCostType.PerMile) {
    // Convert to string with max 2 decimals
    const formatted = value.toFixed(2);

    // Remove trailing zeros and decimal point if not needed
    return formatted.replace(/\.?0{2}$/, '');
  }
  // If flat rate, support only whole numbers
  return value.toFixed(0);
};

const formatProfitByType = (
  value: number,
  costType: CarrierCostType,
  profitType: ProfitType
) => {
  if (profitType === ProfitType.Percentage) {
    return value.toFixed(0);
  }

  // If cost type is per mile and margin type is flat, format margin to 2 decimal places
  if (costType === CarrierCostType.PerMile) {
    // Convert to string with max 2 decimals
    const formatted = value.toFixed(2);

    // Remove trailing zeros and decimal point if not needed
    return formatted.replace(/\.?0{2}$/, '');
  } else {
    return value.toFixed(0);
  }
};
