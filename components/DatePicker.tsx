import { useState } from 'react';
import { ControllerRenderProps } from 'react-hook-form';

import dayjs from 'dayjs';
import { CalendarIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { FieldAttributes } from 'types/LoadAttributes';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';
import { cn } from 'utils/shadcn';

import { Calendar } from './Calendar';
import { Popover, PopoverContent, PopoverTrigger } from './Popover';

export interface DatePickerProps {
  field: ControllerRenderProps<any>;
  thisFieldAttr?: FieldAttributes;
  highlightDirtyField?: boolean;
  highlightError?: boolean;
  logProperties?: Record<string, any>;
}

function DatePicker({
  field,
  highlightDirtyField,
  highlightError,
  thisFieldAttr,
  logProperties,
}: DatePickerProps) {
  const [isCalendarPopoverOpen, setIsCalendarPopoverOpen] = useState(false);

  return (
    <Popover
      open={isCalendarPopoverOpen}
      onOpenChange={(open) => setIsCalendarPopoverOpen(open)}
    >
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className={cn(
            'w-full justify-start text-left font-normal rounded-[4px] border-grayscale-border-input hover:border-grayscale-content-3 py-1 h-8',
            !field.value && 'text-muted-foreground',
            highlightDirtyField && 'bg-yellow-50',
            highlightError && 'bg-red-50'
          )}
          buttonNamePosthog={ButtonNamePosthog.ToggleDatePicker}
          logProperties={logProperties}
          disabled={thisFieldAttr?.isReadOnly}
        >
          <CalendarIcon className='mr-2 h-4 w-4 shrink-0 stroke-grayscale-icon-stroke' />
          <span className='text-[13px] text-grayscale-content-input'>
            {field.value
              ? dayjs(field.value as Date).format('MM/DD/YY')
              : 'Pick a date'}
          </span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0'>
        <Calendar
          mode='single'
          selected={
            field.value
              ? dayjs.isDayjs(field.value)
                ? field.value.toDate()
                : new Date(field.value as string)
              : undefined
          }
          onSelect={(newValue: any) => {
            if (typeof newValue === 'undefined') {
              field.onChange(null);
              return;
            }

            if (field.value && newValue) {
              newValue.setHours(new Date(field.value as string).getHours());
              newValue.setMinutes(new Date(field.value as string).getMinutes());
            }

            field.onChange(newValue);
            setIsCalendarPopoverOpen(false);
          }}
        />
      </PopoverContent>
    </Popover>
  );
}

export { DatePicker };
