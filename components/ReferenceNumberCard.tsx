import { XCircleIcon } from 'lucide-react';

import { Button } from 'components/Button';
import { AdditionalReference } from 'types/Load';
import ButtonNamePosthog from 'types/enums/ButtonNamePosthog';

interface ReferenceNumberCardProps {
  reference: AdditionalReference;
  index: number;
  onRemove: (index: number) => void;
  onEdit: (index: number) => void;
}

export function ReferenceNumberCard({
  reference,
  index,
  onRemove,
  onEdit,
}: ReferenceNumberCardProps): JSX.Element {
  return (
    <div className='flex-shrink-0 w-60 h-44 p-3 bg-gray-50 rounded-md border relative flex flex-col transition-opacity duration-300'>
      {/* Remove button positioned at top-right */}
      <button
        type='button'
        onClick={() => onRemove(index)}
        className='absolute top-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full p-1 z-10'
        title='Remove reference'
      >
        <XCircleIcon className='h-4 w-4' />
      </button>

      {/* Reference content - flex-grow to take available space */}
      <div className='flex-grow space-y-2 text-sm pr-8'>
        <div className='grid grid-cols-2 gap-2 mx-0 w-full'>
          <div>
            <span className='font-bold text-gray-600'>Qualifier:</span>
            <div className='text-gray-900 font-medium truncate'>
              {reference.qualifier || 'N/A'}
            </div>
          </div>
          <div>
            <span className='font-bold text-gray-600'>Number:</span>
            <div className='text-gray-900 font-medium break-words line-clamp-2'>
              {reference.number || 'N/A'}
            </div>
          </div>
        </div>
        <div className='grid grid-cols-2 gap-2 mx-0 w-full'>
          <div>
            <span className='font-bold text-gray-600 text-xs'>Weight:</span>
            <div className='text-gray-900 font-medium'>
              {reference.weight || 0}
            </div>
          </div>
          <div>
            <span className='font-bold text-gray-600 text-xs'>Pieces:</span>
            <div className='text-gray-900 font-medium'>
              {reference.pieces || 0}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom section - always at bottom */}
      <div className='mt-auto pt-2 border-t border-gray-200 grid grid-cols-3 gap-1 mx-0 w-full'>
        {reference.shouldSendToDriver ? (
          <div className='col-span-2 flex items-center'>
            <span className='text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded'>
              Send to driver
            </span>
          </div>
        ) : (
          <div className='col-span-2 flex items-center'>
            <span className='text-xs bg-red-100 text-red-800 px-2 py-1 rounded'>
              Don&apos;t send to driver
            </span>
          </div>
        )}
        <Button
          type='button'
          variant='ghost'
          size='sm'
          onClick={() => onEdit(index)}
          className='w-full h-7 px-2 text-xs hover:bg-gray-100 col-span-1'
          buttonNamePosthog={
            ButtonNamePosthog.LoadBuildingEditMcleodReferenceNumber
          }
        >
          Edit
        </Button>
      </div>
    </div>
  );
}
