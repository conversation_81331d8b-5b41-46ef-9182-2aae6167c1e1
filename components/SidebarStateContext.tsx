import { ReactNode, useEffect, useState } from 'react';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore getSentry is in the parent dir
import { getSentry } from '@utils/sentry/getSentry';

import {
  DrumkitPlatform,
  SidebarState,
  SidebarStateContext,
  defaultSidebarState,
} from 'contexts/sidebarStateContext';

// Utility to set all SidebarState values as Sentry tags with dot notation
function setSentrySidebarStateTags(state: SidebarState) {
  const sentryScope = getSentry();
  Object.entries(state).forEach(([key, value]) => {
    // Only set primitive values (string, number, boolean)
    if (
      Boolean(value) &&
      (typeof value === 'string' ||
        typeof value === 'number' ||
        typeof value === 'boolean')
    ) {
      sentryScope.setTag(`sidebarState.${key}`, String(value));
    }
  });
}

type SidebarBaseProps = {
  drumkitPlatform: DrumkitPlatform;
  children: ReactNode;
};

export type SidebarStateProviderProps = SidebarBaseProps & {
  inboxEmailAddress?: string; // Available in email client platforms (Gmail, Outlook, etc.)
  subject?: string; // Available in email client platforms (Gmail, Outlook, etc.); helpful for debugging
  threadId?: string; // Available in email client platforms (Gmail, Outlook, etc.)
  threadItemId?: string; // Available in email client platforms (Gmail, Outlook, etc.)
  isOutlookReply?: boolean; // Available in Outlook
  isChromeSidePanel?: boolean; // Available only when running as Google Chrome extension
  // Available only in Chrome extension; note that this is the tab ID of the currently running sidepanel
  // and not the tab ID of the currently active tab.
  // Because the sidepanel runs independent of any webpage context, it's critical to use this to determine
  // if the sidepanel is the intended target of a chrome.runtime message.
  tabId?: number;
};

export default function SidebarStateProvider({
  drumkitPlatform,
  children,
  inboxEmailAddress,
  subject,
  threadId,
  threadItemId,
  isOutlookReply,
  isChromeSidePanel,
  tabId,
}: SidebarStateProviderProps) {
  const [currentState, setCurrentState] = useState<SidebarState>({
    ...defaultSidebarState,
    tabId: tabId ?? 0,
    drumkitPlatform: drumkitPlatform,
    inboxEmailAddress: inboxEmailAddress ?? '',
    subject: subject ?? '',
    threadId: threadId ?? '',
    threadItemId: threadItemId ?? '',
    isOutlookReply: isOutlookReply ?? false,
    isChromeSidePanel: isChromeSidePanel ?? false,
  });

  // Set all sidebar state as Sentry tags
  useEffect(() => {
    setSentrySidebarStateTags(currentState);
  }, [currentState]);

  useEffect(() => {
    setCurrentState((prevState) => ({
      // Only reset state if thread ID has changed; prevents suggestions from being reset to empty
      ...(prevState.threadId === threadId ? prevState : defaultSidebarState),
      drumkitPlatform: drumkitPlatform,
      inboxEmailAddress: inboxEmailAddress ?? '',
      subject: subject ?? '',
      threadId: threadId ?? '',
      threadItemId: threadItemId ?? '',
      isOutlookReply: isOutlookReply ?? false,
      isChromeSidePanel: isChromeSidePanel ?? false,
      tabId: tabId ?? 0,
    }));
  }, [
    drumkitPlatform,
    inboxEmailAddress,
    subject,
    threadId,
    threadItemId,
    isOutlookReply,
    isChromeSidePanel,
    tabId,
  ]);

  return (
    <SidebarStateContext.Provider
      value={{
        currentState: currentState,
        setCurrentState: setCurrentState,
      }}
    >
      {children}
    </SidebarStateContext.Provider>
  );
}
