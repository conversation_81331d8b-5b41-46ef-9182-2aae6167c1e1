import * as React from 'react';

import E2open from '../assets/e2open-logo.png';
import { cn } from 'utils/shadcn';

export default function E2openLogo(
  props: React.ImgHTMLAttributes<HTMLImageElement>
): JSX.Element {
  const { className: classNameProp, ...otherProps } = props;

  return (
    <img src={E2open} alt='E2open logo' className={cn('filter brightness-50', classNameProp)} {...otherProps} />
  );
}
