import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { formatTimestamp } from './time';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);

const clientDefaultLocale = dayjs.tz().format('z');
dayjs.locale();

const isEasternTime = () => {
  const timezone = dayjs().tz().format('z');
  return timezone === 'EDT' || timezone === 'EST';
};

describe('formatTimestamp', () => {
  it('should format the date in target timezone correctly', () => {
    const date = dayjs('2024-03-30T15:30:00Z');
    const formattedStr = formatTimestamp(
      date,
      'MM/DD HH:mm z',
      'America/Los_Angeles'
    );
    expect(formattedStr).toBe('03/30 08:30 PDT');
  });

  it("should format the date without 'z' in format in target timezone", () => {
    const date = dayjs('2024-03-30T15:30:00Z');
    const noTZInFormat = formatTimestamp(
      date,
      'MM/DD HH:mm',
      'America/Los_Angeles'
    );
    expect(noTZInFormat).toBe('03/30 08:30');
  });

  it("should format the date without 'z' in format in target timezone", () => {
    const date = dayjs('2024-03-30T15:30:00Z');
    const noTZFormatOrParam = formatTimestamp(
      date,
      'MM/DD HH:mm',
      'America/Los_Angeles'
    );
    expect(noTZFormatOrParam).toBe('03/30 08:30');
  });

  // Test dependent on client locale
  if (!isEasternTime()) {
    it.skip("should format the date without timezone param to user's locale", () => {});
  } else {
    it("should format the date without timezone param to user's locale", () => {
      const date = dayjs('2024-03-30T15:30:00Z');
      const noTZParam = formatTimestamp(date, 'MM/DD HH:mm z');
      expect(noTZParam).toBe('03/30 11:30 ' + clientDefaultLocale); // Test running in ET locale
    });
  }

  if (!isEasternTime()) {
    it.skip("should format the date without 'z' in format in user's locale", () => {});
  } else {
    it("should format the date without 'z' in format in user's locale", () => {
      const date = dayjs('2024-03-30T15:30:00Z');
      const noTZFormatOrParam = formatTimestamp(date, 'MM/DD HH:mm');
      expect(noTZFormatOrParam).toBe('03/30 11:30'); // Test running in EDT locale
    });
  }
});
