import { isLikelyPhoneNumber } from '../phoneAndDateParser';

describe('isLikelyPhoneNumber', () => {
  // Common phone number formats should return true
  test('recognizes standard phone number formats', () => {
    expect(isLikelyPhoneNumber('************')).toBe(true);
    expect(isLikelyPhoneNumber('(*************')).toBe(true);
    expect(isLikelyPhoneNumber('5551234567')).toBe(true);
    expect(isLikelyPhoneNumber('1-************')).toBe(true);
    expect(isLikelyPhoneNumber('+15551234567')).toBe(true);
    expect(isLikelyPhoneNumber('************')).toBe(true);
  });

  // Date strings should return false
  test('rejects date strings', () => {
    expect(isLikelyPhoneNumber('2023-01-15')).toBe(false);
    expect(isLikelyPhoneNumber('01/15/2023')).toBe(false);
    expect(isLikelyPhoneNumber('15/01/2023')).toBe(false);
    expect(isLikelyPhoneNumber('Jan 15, 2023')).toBe(false);
  });

  // Datetime strings should return false
  test('rejects datetime strings', () => {
    expect(isLikelyPhoneNumber('2023-01-15 13:45')).toBe(false);
    expect(isLikelyPhoneNumber('2023-01-15T13:45:00')).toBe(false);
    expect(isLikelyPhoneNumber('01/15/2023 1:45 PM')).toBe(false);
    expect(isLikelyPhoneNumber('Jan 15, 2023 13:45')).toBe(false);
  });

  // ISO format timestamps should return false
  test('rejects ISO format timestamps', () => {
    expect(isLikelyPhoneNumber('2023-01-15T13:45:00Z')).toBe(false);
    expect(isLikelyPhoneNumber('2023-01-15T13:45:00.123Z')).toBe(false);
    expect(isLikelyPhoneNumber('2023-01-15T13:45:00+01:00')).toBe(false);
  });

  test('rejects Unix timestamps', () => {
    expect(isLikelyPhoneNumber('1673790300')).toBe(false);
    expect(isLikelyPhoneNumber('1673790300000')).toBe(false);
    const recentTimestamp = Math.floor(Date.now() / 1000).toString();
    expect(isLikelyPhoneNumber(recentTimestamp)).toBe(false);
  });

  test('handles edge cases correctly', () => {
    expect(isLikelyPhoneNumber('12345')).toBe(false);
    expect(isLikelyPhoneNumber(12345678901)).toBe(true);
    expect(isLikelyPhoneNumber('')).toBe(false);
    expect(isLikelyPhoneNumber(undefined as any)).toBe(false);
    const futureTimestamp = (
      Math.floor(Date.now() / 1000) +
      86400 * 365
    ).toString(); // ~1 year in future
    expect(isLikelyPhoneNumber(futureTimestamp)).toBe(false);
  });
});
