import dayjs from 'dayjs';

import { isValidNonDateObject, objectHasTruthyValues } from '../isValidObject';

describe('isValidNonDateObject', () => {
  test('should return true for a plain object', () => {
    const obj = { a: 1 };
    expect(isValidNonDateObject(obj)).toBe(true);
  });

  test('should return false for a Date object', () => {
    const date = new Date();
    expect(isValidNonDateObject(date)).toBe(false);
  });

  test('should return false for a dayjs object', () => {
    const dayjsObj = dayjs();
    expect(isValidNonDateObject(dayjsObj)).toBe(false);
  });

  test('should return false for primitive types', () => {
    expect(isValidNonDateObject(42)).toBe(false);
    expect(isValidNonDateObject('string')).toBe(false);
    expect(isValidNonDateObject(null)).toBe(false);
    expect(isValidNonDateObject(undefined)).toBe(false);
    expect(isValidNonDateObject(true)).toBe(false);
  });

  test('should return true for arrays', () => {
    const arr = [1, 2, 3];
    expect(isValidNonDateObject(arr)).toBe(true);
  });
});

describe('objectHasTruthyValues', () => {
  test('should return true if the object has truthy values', () => {
    const obj = { a: 0, b: 'string', c: false };
    expect(objectHasTruthyValues(obj)).toBe(true);
  });

  test('should return false if the object has no truthy values', () => {
    const obj = { a: 0, b: false, c: null, d: undefined };
    expect(objectHasTruthyValues(obj)).toBe(false);
  });

  test('should return true for nested objects with truthy values', () => {
    const obj = { a: { b: { c: 'value' } }, d: null };
    expect(objectHasTruthyValues(obj)).toBe(true);
  });

  test('should return false for nested objects with no truthy values', () => {
    const obj = { a: { b: { c: 0 } }, d: false };
    expect(objectHasTruthyValues(obj)).toBe(false);
  });

  test('should only check the subset of keys when provided', () => {
    const obj = { a: 0, b: 'string', c: false };
    expect(objectHasTruthyValues(obj, ['a'])).toBe(false);
    expect(objectHasTruthyValues(obj, ['b'])).toBe(true);
    expect(objectHasTruthyValues(obj, ['c'])).toBe(false);
  });

  test('should work with empty objects', () => {
    const obj = {};
    expect(objectHasTruthyValues(obj)).toBe(false);
  });

  test('should work with subset keys for nested objects', () => {
    const obj = { a: { b: { c: 'value', d: 0 }, e: false } };
    expect(objectHasTruthyValues(obj, ['a'])).toBe(true);
    expect(objectHasTruthyValues(obj.a, ['b', 'e'])).toBe(true);
    expect(objectHasTruthyValues(obj.a.b, ['d'])).toBe(false);
  });
});
