import { getValidChangeElements } from 'components/AISuggestions/SuggestionsCarousel';
import { LoadBuildingSuggestions } from 'types/suggestions/LoadBuildingSuggestions';
import { SuggestedQuoteChange } from 'types/suggestions/QuoteSuggestions';

import { flattenSuggestionChanges } from './flattenSuggestionChanges';

type AllowedSuggestionTypes = SuggestedQuoteChange | LoadBuildingSuggestions;

export const filterInvalidSuggestions = <T extends AllowedSuggestionTypes>(
  suggestions: T[]
): T[] =>
  suggestions.filter(
    (s) =>
      getValidChangeElements(flattenSuggestionChanges(s.suggested), s).elements
        .length > 0
  );
